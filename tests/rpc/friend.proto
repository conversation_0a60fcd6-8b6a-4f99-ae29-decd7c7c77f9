syntax = "proto3";

package fixbug;  //所在的命名空间

option cc_generic_services = true;  //开启桩stub服务

message ResultCode
{
    int32 errcode = 1;
    bytes errmsg = 2;
}

message GetFriendsListRequest  //请求，响应
{
    uint32 userid = 1;
}

message GetFriendsListResponse  //请求，响应
{
    ResultCode result = 1;
    repeated bytes friends = 2;
}

// 好友模块
service FriendServiceRpc  //具体的服务模块和服务方法
{
    rpc GetFriendsList(GetFriendsListRequest) returns(GetFriendsListResponse);
}