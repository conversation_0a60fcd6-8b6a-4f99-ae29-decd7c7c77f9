// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: raftRpcPro/raftRPC.proto

#include "raftRpcPro/raftRPC.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG

namespace _pb = ::PROTOBUF_NAMESPACE_ID;
namespace _pbi = _pb::internal;

namespace raftRpcProctoc {
PROTOBUF_CONSTEXPR ConfigChange::ConfigChange(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.nodeid_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.address_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.type_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct ConfigChangeDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ConfigChangeDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~ConfigChangeDefaultTypeInternal() {}
  union {
    ConfigChange _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ConfigChangeDefaultTypeInternal _ConfigChange_default_instance_;
PROTOBUF_CONSTEXPR LogEntry::LogEntry(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.command_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.configchange_)*/nullptr
  , /*decltype(_impl_.logterm_)*/0
  , /*decltype(_impl_.logindex_)*/0
  , /*decltype(_impl_.isconfigchange_)*/false
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct LogEntryDefaultTypeInternal {
  PROTOBUF_CONSTEXPR LogEntryDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~LogEntryDefaultTypeInternal() {}
  union {
    LogEntry _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 LogEntryDefaultTypeInternal _LogEntry_default_instance_;
PROTOBUF_CONSTEXPR AppendEntriesArgs::AppendEntriesArgs(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.entries_)*/{}
  , /*decltype(_impl_.term_)*/0
  , /*decltype(_impl_.leaderid_)*/0
  , /*decltype(_impl_.prevlogindex_)*/0
  , /*decltype(_impl_.prevlogterm_)*/0
  , /*decltype(_impl_.leadercommit_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct AppendEntriesArgsDefaultTypeInternal {
  PROTOBUF_CONSTEXPR AppendEntriesArgsDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~AppendEntriesArgsDefaultTypeInternal() {}
  union {
    AppendEntriesArgs _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 AppendEntriesArgsDefaultTypeInternal _AppendEntriesArgs_default_instance_;
PROTOBUF_CONSTEXPR AppendEntriesReply::AppendEntriesReply(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.term_)*/0
  , /*decltype(_impl_.success_)*/false
  , /*decltype(_impl_.updatenextindex_)*/0
  , /*decltype(_impl_.appstate_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct AppendEntriesReplyDefaultTypeInternal {
  PROTOBUF_CONSTEXPR AppendEntriesReplyDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~AppendEntriesReplyDefaultTypeInternal() {}
  union {
    AppendEntriesReply _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 AppendEntriesReplyDefaultTypeInternal _AppendEntriesReply_default_instance_;
PROTOBUF_CONSTEXPR RequestVoteArgs::RequestVoteArgs(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.term_)*/0
  , /*decltype(_impl_.candidateid_)*/0
  , /*decltype(_impl_.lastlogindex_)*/0
  , /*decltype(_impl_.lastlogterm_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct RequestVoteArgsDefaultTypeInternal {
  PROTOBUF_CONSTEXPR RequestVoteArgsDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~RequestVoteArgsDefaultTypeInternal() {}
  union {
    RequestVoteArgs _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 RequestVoteArgsDefaultTypeInternal _RequestVoteArgs_default_instance_;
PROTOBUF_CONSTEXPR RequestVoteReply::RequestVoteReply(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.term_)*/0
  , /*decltype(_impl_.votegranted_)*/false
  , /*decltype(_impl_.votestate_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct RequestVoteReplyDefaultTypeInternal {
  PROTOBUF_CONSTEXPR RequestVoteReplyDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~RequestVoteReplyDefaultTypeInternal() {}
  union {
    RequestVoteReply _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 RequestVoteReplyDefaultTypeInternal _RequestVoteReply_default_instance_;
PROTOBUF_CONSTEXPR InstallSnapshotRequest::InstallSnapshotRequest(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.data_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.leaderid_)*/0
  , /*decltype(_impl_.term_)*/0
  , /*decltype(_impl_.lastsnapshotincludeindex_)*/0
  , /*decltype(_impl_.lastsnapshotincludeterm_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct InstallSnapshotRequestDefaultTypeInternal {
  PROTOBUF_CONSTEXPR InstallSnapshotRequestDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~InstallSnapshotRequestDefaultTypeInternal() {}
  union {
    InstallSnapshotRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 InstallSnapshotRequestDefaultTypeInternal _InstallSnapshotRequest_default_instance_;
PROTOBUF_CONSTEXPR InstallSnapshotResponse::InstallSnapshotResponse(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.term_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct InstallSnapshotResponseDefaultTypeInternal {
  PROTOBUF_CONSTEXPR InstallSnapshotResponseDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~InstallSnapshotResponseDefaultTypeInternal() {}
  union {
    InstallSnapshotResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 InstallSnapshotResponseDefaultTypeInternal _InstallSnapshotResponse_default_instance_;
PROTOBUF_CONSTEXPR ChangeConfigArgs::ChangeConfigArgs(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.nodeid_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.address_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.type_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct ChangeConfigArgsDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ChangeConfigArgsDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~ChangeConfigArgsDefaultTypeInternal() {}
  union {
    ChangeConfigArgs _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ChangeConfigArgsDefaultTypeInternal _ChangeConfigArgs_default_instance_;
PROTOBUF_CONSTEXPR ChangeConfigReply::ChangeConfigReply(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.error_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.success_)*/false
  , /*decltype(_impl_.isleader_)*/false
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct ChangeConfigReplyDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ChangeConfigReplyDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~ChangeConfigReplyDefaultTypeInternal() {}
  union {
    ChangeConfigReply _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ChangeConfigReplyDefaultTypeInternal _ChangeConfigReply_default_instance_;
}  // namespace raftRpcProctoc
static ::_pb::Metadata file_level_metadata_raftRpcPro_2fraftRPC_2eproto[10];
static const ::_pb::EnumDescriptor* file_level_enum_descriptors_raftRpcPro_2fraftRPC_2eproto[1];
static const ::_pb::ServiceDescriptor* file_level_service_descriptors_raftRpcPro_2fraftRPC_2eproto[1];

const uint32_t TableStruct_raftRpcPro_2fraftRPC_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::ConfigChange, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::ConfigChange, _impl_.type_),
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::ConfigChange, _impl_.nodeid_),
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::ConfigChange, _impl_.address_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::LogEntry, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::LogEntry, _impl_.command_),
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::LogEntry, _impl_.logterm_),
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::LogEntry, _impl_.logindex_),
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::LogEntry, _impl_.isconfigchange_),
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::LogEntry, _impl_.configchange_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::AppendEntriesArgs, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::AppendEntriesArgs, _impl_.term_),
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::AppendEntriesArgs, _impl_.leaderid_),
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::AppendEntriesArgs, _impl_.prevlogindex_),
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::AppendEntriesArgs, _impl_.prevlogterm_),
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::AppendEntriesArgs, _impl_.entries_),
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::AppendEntriesArgs, _impl_.leadercommit_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::AppendEntriesReply, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::AppendEntriesReply, _impl_.term_),
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::AppendEntriesReply, _impl_.success_),
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::AppendEntriesReply, _impl_.updatenextindex_),
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::AppendEntriesReply, _impl_.appstate_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::RequestVoteArgs, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::RequestVoteArgs, _impl_.term_),
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::RequestVoteArgs, _impl_.candidateid_),
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::RequestVoteArgs, _impl_.lastlogindex_),
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::RequestVoteArgs, _impl_.lastlogterm_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::RequestVoteReply, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::RequestVoteReply, _impl_.term_),
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::RequestVoteReply, _impl_.votegranted_),
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::RequestVoteReply, _impl_.votestate_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::InstallSnapshotRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::InstallSnapshotRequest, _impl_.leaderid_),
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::InstallSnapshotRequest, _impl_.term_),
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::InstallSnapshotRequest, _impl_.lastsnapshotincludeindex_),
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::InstallSnapshotRequest, _impl_.lastsnapshotincludeterm_),
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::InstallSnapshotRequest, _impl_.data_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::InstallSnapshotResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::InstallSnapshotResponse, _impl_.term_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::ChangeConfigArgs, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::ChangeConfigArgs, _impl_.type_),
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::ChangeConfigArgs, _impl_.nodeid_),
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::ChangeConfigArgs, _impl_.address_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::ChangeConfigReply, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::ChangeConfigReply, _impl_.success_),
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::ChangeConfigReply, _impl_.error_),
  PROTOBUF_FIELD_OFFSET(::raftRpcProctoc::ChangeConfigReply, _impl_.isleader_),
};
static const ::_pbi::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::raftRpcProctoc::ConfigChange)},
  { 9, -1, -1, sizeof(::raftRpcProctoc::LogEntry)},
  { 20, -1, -1, sizeof(::raftRpcProctoc::AppendEntriesArgs)},
  { 32, -1, -1, sizeof(::raftRpcProctoc::AppendEntriesReply)},
  { 42, -1, -1, sizeof(::raftRpcProctoc::RequestVoteArgs)},
  { 52, -1, -1, sizeof(::raftRpcProctoc::RequestVoteReply)},
  { 61, -1, -1, sizeof(::raftRpcProctoc::InstallSnapshotRequest)},
  { 72, -1, -1, sizeof(::raftRpcProctoc::InstallSnapshotResponse)},
  { 79, -1, -1, sizeof(::raftRpcProctoc::ChangeConfigArgs)},
  { 88, -1, -1, sizeof(::raftRpcProctoc::ChangeConfigReply)},
};

static const ::_pb::Message* const file_default_instances[] = {
  &::raftRpcProctoc::_ConfigChange_default_instance_._instance,
  &::raftRpcProctoc::_LogEntry_default_instance_._instance,
  &::raftRpcProctoc::_AppendEntriesArgs_default_instance_._instance,
  &::raftRpcProctoc::_AppendEntriesReply_default_instance_._instance,
  &::raftRpcProctoc::_RequestVoteArgs_default_instance_._instance,
  &::raftRpcProctoc::_RequestVoteReply_default_instance_._instance,
  &::raftRpcProctoc::_InstallSnapshotRequest_default_instance_._instance,
  &::raftRpcProctoc::_InstallSnapshotResponse_default_instance_._instance,
  &::raftRpcProctoc::_ChangeConfigArgs_default_instance_._instance,
  &::raftRpcProctoc::_ChangeConfigReply_default_instance_._instance,
};

const char descriptor_table_protodef_raftRpcPro_2fraftRPC_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\030raftRpcPro/raftRPC.proto\022\016raftRpcProct"
  "oc\"_\n\014ConfigChange\022.\n\004Type\030\001 \001(\0162 .raftR"
  "pcProctoc.ConfigChangeType\022\016\n\006NodeId\030\002 \001"
  "(\t\022\017\n\007Address\030\003 \001(\t\"\212\001\n\010LogEntry\022\017\n\007Comm"
  "and\030\001 \001(\014\022\017\n\007LogTerm\030\002 \001(\005\022\020\n\010LogIndex\030\003"
  " \001(\005\022\026\n\016IsConfigChange\030\004 \001(\010\0222\n\014ConfigCh"
  "ange\030\005 \001(\0132\034.raftRpcProctoc.ConfigChange"
  "\"\237\001\n\021AppendEntriesArgs\022\014\n\004Term\030\001 \001(\005\022\020\n\010"
  "LeaderId\030\002 \001(\005\022\024\n\014PrevLogIndex\030\003 \001(\005\022\023\n\013"
  "PrevLogTerm\030\004 \001(\005\022)\n\007Entries\030\005 \003(\0132\030.raf"
  "tRpcProctoc.LogEntry\022\024\n\014LeaderCommit\030\006 \001"
  "(\005\"^\n\022AppendEntriesReply\022\014\n\004Term\030\001 \001(\005\022\017"
  "\n\007Success\030\002 \001(\010\022\027\n\017UpdateNextIndex\030\003 \001(\005"
  "\022\020\n\010AppState\030\004 \001(\005\"_\n\017RequestVoteArgs\022\014\n"
  "\004Term\030\001 \001(\005\022\023\n\013CandidateId\030\002 \001(\005\022\024\n\014Last"
  "LogIndex\030\003 \001(\005\022\023\n\013LastLogTerm\030\004 \001(\005\"H\n\020R"
  "equestVoteReply\022\014\n\004Term\030\001 \001(\005\022\023\n\013VoteGra"
  "nted\030\002 \001(\010\022\021\n\tVoteState\030\003 \001(\005\"\211\001\n\026Instal"
  "lSnapshotRequest\022\020\n\010LeaderId\030\001 \001(\005\022\014\n\004Te"
  "rm\030\002 \001(\005\022 \n\030LastSnapShotIncludeIndex\030\003 \001"
  "(\005\022\037\n\027LastSnapShotIncludeTerm\030\004 \001(\005\022\014\n\004D"
  "ata\030\005 \001(\014\"\'\n\027InstallSnapshotResponse\022\014\n\004"
  "Term\030\001 \001(\005\"c\n\020ChangeConfigArgs\022.\n\004Type\030\001"
  " \001(\0162 .raftRpcProctoc.ConfigChangeType\022\016"
  "\n\006NodeId\030\002 \001(\t\022\017\n\007Address\030\003 \001(\t\"E\n\021Chang"
  "eConfigReply\022\017\n\007Success\030\001 \001(\010\022\r\n\005Error\030\002"
  " \001(\t\022\020\n\010IsLeader\030\003 \001(\010*1\n\020ConfigChangeTy"
  "pe\022\014\n\010ADD_NODE\020\000\022\017\n\013REMOVE_NODE\020\0012\354\002\n\007ra"
  "ftRpc\022V\n\rAppendEntries\022!.raftRpcProctoc."
  "AppendEntriesArgs\032\".raftRpcProctoc.Appen"
  "dEntriesReply\022b\n\017InstallSnapshot\022&.raftR"
  "pcProctoc.InstallSnapshotRequest\032\'.raftR"
  "pcProctoc.InstallSnapshotResponse\022P\n\013Req"
  "uestVote\022\037.raftRpcProctoc.RequestVoteArg"
  "s\032 .raftRpcProctoc.RequestVoteReply\022S\n\014C"
  "hangeConfig\022 .raftRpcProctoc.ChangeConfi"
  "gArgs\032!.raftRpcProctoc.ChangeConfigReply"
  "B\003\200\001\001b\006proto3"
  ;
static ::_pbi::once_flag descriptor_table_raftRpcPro_2fraftRPC_2eproto_once;
const ::_pbi::DescriptorTable descriptor_table_raftRpcPro_2fraftRPC_2eproto = {
    false, false, 1493, descriptor_table_protodef_raftRpcPro_2fraftRPC_2eproto,
    "raftRpcPro/raftRPC.proto",
    &descriptor_table_raftRpcPro_2fraftRPC_2eproto_once, nullptr, 0, 10,
    schemas, file_default_instances, TableStruct_raftRpcPro_2fraftRPC_2eproto::offsets,
    file_level_metadata_raftRpcPro_2fraftRPC_2eproto, file_level_enum_descriptors_raftRpcPro_2fraftRPC_2eproto,
    file_level_service_descriptors_raftRpcPro_2fraftRPC_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::_pbi::DescriptorTable* descriptor_table_raftRpcPro_2fraftRPC_2eproto_getter() {
  return &descriptor_table_raftRpcPro_2fraftRPC_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY2 static ::_pbi::AddDescriptorsRunner dynamic_init_dummy_raftRpcPro_2fraftRPC_2eproto(&descriptor_table_raftRpcPro_2fraftRPC_2eproto);
namespace raftRpcProctoc {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ConfigChangeType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_raftRpcPro_2fraftRPC_2eproto);
  return file_level_enum_descriptors_raftRpcPro_2fraftRPC_2eproto[0];
}
bool ConfigChangeType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class ConfigChange::_Internal {
 public:
};

ConfigChange::ConfigChange(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:raftRpcProctoc.ConfigChange)
}
ConfigChange::ConfigChange(const ConfigChange& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  ConfigChange* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.nodeid_){}
    , decltype(_impl_.address_){}
    , decltype(_impl_.type_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.nodeid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.nodeid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_nodeid().empty()) {
    _this->_impl_.nodeid_.Set(from._internal_nodeid(), 
      _this->GetArenaForAllocation());
  }
  _impl_.address_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.address_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_address().empty()) {
    _this->_impl_.address_.Set(from._internal_address(), 
      _this->GetArenaForAllocation());
  }
  _this->_impl_.type_ = from._impl_.type_;
  // @@protoc_insertion_point(copy_constructor:raftRpcProctoc.ConfigChange)
}

inline void ConfigChange::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.nodeid_){}
    , decltype(_impl_.address_){}
    , decltype(_impl_.type_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.nodeid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.nodeid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.address_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.address_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

ConfigChange::~ConfigChange() {
  // @@protoc_insertion_point(destructor:raftRpcProctoc.ConfigChange)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void ConfigChange::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.nodeid_.Destroy();
  _impl_.address_.Destroy();
}

void ConfigChange::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void ConfigChange::Clear() {
// @@protoc_insertion_point(message_clear_start:raftRpcProctoc.ConfigChange)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.nodeid_.ClearToEmpty();
  _impl_.address_.ClearToEmpty();
  _impl_.type_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ConfigChange::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .raftRpcProctoc.ConfigChangeType Type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_type(static_cast<::raftRpcProctoc::ConfigChangeType>(val));
        } else
          goto handle_unusual;
        continue;
      // string NodeId = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_nodeid();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "raftRpcProctoc.ConfigChange.NodeId"));
        } else
          goto handle_unusual;
        continue;
      // string Address = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_address();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "raftRpcProctoc.ConfigChange.Address"));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ConfigChange::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:raftRpcProctoc.ConfigChange)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .raftRpcProctoc.ConfigChangeType Type = 1;
  if (this->_internal_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      1, this->_internal_type(), target);
  }

  // string NodeId = 2;
  if (!this->_internal_nodeid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_nodeid().data(), static_cast<int>(this->_internal_nodeid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "raftRpcProctoc.ConfigChange.NodeId");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_nodeid(), target);
  }

  // string Address = 3;
  if (!this->_internal_address().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_address().data(), static_cast<int>(this->_internal_address().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "raftRpcProctoc.ConfigChange.Address");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_address(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:raftRpcProctoc.ConfigChange)
  return target;
}

size_t ConfigChange::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:raftRpcProctoc.ConfigChange)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string NodeId = 2;
  if (!this->_internal_nodeid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_nodeid());
  }

  // string Address = 3;
  if (!this->_internal_address().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_address());
  }

  // .raftRpcProctoc.ConfigChangeType Type = 1;
  if (this->_internal_type() != 0) {
    total_size += 1 +
      ::_pbi::WireFormatLite::EnumSize(this->_internal_type());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ConfigChange::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    ConfigChange::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ConfigChange::GetClassData() const { return &_class_data_; }


void ConfigChange::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<ConfigChange*>(&to_msg);
  auto& from = static_cast<const ConfigChange&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:raftRpcProctoc.ConfigChange)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_nodeid().empty()) {
    _this->_internal_set_nodeid(from._internal_nodeid());
  }
  if (!from._internal_address().empty()) {
    _this->_internal_set_address(from._internal_address());
  }
  if (from._internal_type() != 0) {
    _this->_internal_set_type(from._internal_type());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ConfigChange::CopyFrom(const ConfigChange& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:raftRpcProctoc.ConfigChange)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ConfigChange::IsInitialized() const {
  return true;
}

void ConfigChange::InternalSwap(ConfigChange* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.nodeid_, lhs_arena,
      &other->_impl_.nodeid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.address_, lhs_arena,
      &other->_impl_.address_, rhs_arena
  );
  swap(_impl_.type_, other->_impl_.type_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ConfigChange::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_raftRpcPro_2fraftRPC_2eproto_getter, &descriptor_table_raftRpcPro_2fraftRPC_2eproto_once,
      file_level_metadata_raftRpcPro_2fraftRPC_2eproto[0]);
}

// ===================================================================

class LogEntry::_Internal {
 public:
  static const ::raftRpcProctoc::ConfigChange& configchange(const LogEntry* msg);
};

const ::raftRpcProctoc::ConfigChange&
LogEntry::_Internal::configchange(const LogEntry* msg) {
  return *msg->_impl_.configchange_;
}
LogEntry::LogEntry(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:raftRpcProctoc.LogEntry)
}
LogEntry::LogEntry(const LogEntry& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  LogEntry* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.command_){}
    , decltype(_impl_.configchange_){nullptr}
    , decltype(_impl_.logterm_){}
    , decltype(_impl_.logindex_){}
    , decltype(_impl_.isconfigchange_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.command_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.command_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_command().empty()) {
    _this->_impl_.command_.Set(from._internal_command(), 
      _this->GetArenaForAllocation());
  }
  if (from._internal_has_configchange()) {
    _this->_impl_.configchange_ = new ::raftRpcProctoc::ConfigChange(*from._impl_.configchange_);
  }
  ::memcpy(&_impl_.logterm_, &from._impl_.logterm_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.isconfigchange_) -
    reinterpret_cast<char*>(&_impl_.logterm_)) + sizeof(_impl_.isconfigchange_));
  // @@protoc_insertion_point(copy_constructor:raftRpcProctoc.LogEntry)
}

inline void LogEntry::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.command_){}
    , decltype(_impl_.configchange_){nullptr}
    , decltype(_impl_.logterm_){0}
    , decltype(_impl_.logindex_){0}
    , decltype(_impl_.isconfigchange_){false}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.command_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.command_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

LogEntry::~LogEntry() {
  // @@protoc_insertion_point(destructor:raftRpcProctoc.LogEntry)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void LogEntry::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.command_.Destroy();
  if (this != internal_default_instance()) delete _impl_.configchange_;
}

void LogEntry::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void LogEntry::Clear() {
// @@protoc_insertion_point(message_clear_start:raftRpcProctoc.LogEntry)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.command_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && _impl_.configchange_ != nullptr) {
    delete _impl_.configchange_;
  }
  _impl_.configchange_ = nullptr;
  ::memset(&_impl_.logterm_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.isconfigchange_) -
      reinterpret_cast<char*>(&_impl_.logterm_)) + sizeof(_impl_.isconfigchange_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LogEntry::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bytes Command = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_command();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 LogTerm = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.logterm_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 LogIndex = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.logindex_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool IsConfigChange = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _impl_.isconfigchange_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .raftRpcProctoc.ConfigChange ConfigChange = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_configchange(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LogEntry::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:raftRpcProctoc.LogEntry)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bytes Command = 1;
  if (!this->_internal_command().empty()) {
    target = stream->WriteBytesMaybeAliased(
        1, this->_internal_command(), target);
  }

  // int32 LogTerm = 2;
  if (this->_internal_logterm() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(2, this->_internal_logterm(), target);
  }

  // int32 LogIndex = 3;
  if (this->_internal_logindex() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(3, this->_internal_logindex(), target);
  }

  // bool IsConfigChange = 4;
  if (this->_internal_isconfigchange() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(4, this->_internal_isconfigchange(), target);
  }

  // .raftRpcProctoc.ConfigChange ConfigChange = 5;
  if (this->_internal_has_configchange()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(5, _Internal::configchange(this),
        _Internal::configchange(this).GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:raftRpcProctoc.LogEntry)
  return target;
}

size_t LogEntry::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:raftRpcProctoc.LogEntry)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes Command = 1;
  if (!this->_internal_command().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_command());
  }

  // .raftRpcProctoc.ConfigChange ConfigChange = 5;
  if (this->_internal_has_configchange()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.configchange_);
  }

  // int32 LogTerm = 2;
  if (this->_internal_logterm() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_logterm());
  }

  // int32 LogIndex = 3;
  if (this->_internal_logindex() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_logindex());
  }

  // bool IsConfigChange = 4;
  if (this->_internal_isconfigchange() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LogEntry::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    LogEntry::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LogEntry::GetClassData() const { return &_class_data_; }


void LogEntry::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<LogEntry*>(&to_msg);
  auto& from = static_cast<const LogEntry&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:raftRpcProctoc.LogEntry)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_command().empty()) {
    _this->_internal_set_command(from._internal_command());
  }
  if (from._internal_has_configchange()) {
    _this->_internal_mutable_configchange()->::raftRpcProctoc::ConfigChange::MergeFrom(
        from._internal_configchange());
  }
  if (from._internal_logterm() != 0) {
    _this->_internal_set_logterm(from._internal_logterm());
  }
  if (from._internal_logindex() != 0) {
    _this->_internal_set_logindex(from._internal_logindex());
  }
  if (from._internal_isconfigchange() != 0) {
    _this->_internal_set_isconfigchange(from._internal_isconfigchange());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LogEntry::CopyFrom(const LogEntry& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:raftRpcProctoc.LogEntry)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LogEntry::IsInitialized() const {
  return true;
}

void LogEntry::InternalSwap(LogEntry* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.command_, lhs_arena,
      &other->_impl_.command_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(LogEntry, _impl_.isconfigchange_)
      + sizeof(LogEntry::_impl_.isconfigchange_)
      - PROTOBUF_FIELD_OFFSET(LogEntry, _impl_.configchange_)>(
          reinterpret_cast<char*>(&_impl_.configchange_),
          reinterpret_cast<char*>(&other->_impl_.configchange_));
}

::PROTOBUF_NAMESPACE_ID::Metadata LogEntry::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_raftRpcPro_2fraftRPC_2eproto_getter, &descriptor_table_raftRpcPro_2fraftRPC_2eproto_once,
      file_level_metadata_raftRpcPro_2fraftRPC_2eproto[1]);
}

// ===================================================================

class AppendEntriesArgs::_Internal {
 public:
};

AppendEntriesArgs::AppendEntriesArgs(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:raftRpcProctoc.AppendEntriesArgs)
}
AppendEntriesArgs::AppendEntriesArgs(const AppendEntriesArgs& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  AppendEntriesArgs* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.entries_){from._impl_.entries_}
    , decltype(_impl_.term_){}
    , decltype(_impl_.leaderid_){}
    , decltype(_impl_.prevlogindex_){}
    , decltype(_impl_.prevlogterm_){}
    , decltype(_impl_.leadercommit_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.term_, &from._impl_.term_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.leadercommit_) -
    reinterpret_cast<char*>(&_impl_.term_)) + sizeof(_impl_.leadercommit_));
  // @@protoc_insertion_point(copy_constructor:raftRpcProctoc.AppendEntriesArgs)
}

inline void AppendEntriesArgs::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.entries_){arena}
    , decltype(_impl_.term_){0}
    , decltype(_impl_.leaderid_){0}
    , decltype(_impl_.prevlogindex_){0}
    , decltype(_impl_.prevlogterm_){0}
    , decltype(_impl_.leadercommit_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

AppendEntriesArgs::~AppendEntriesArgs() {
  // @@protoc_insertion_point(destructor:raftRpcProctoc.AppendEntriesArgs)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void AppendEntriesArgs::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.entries_.~RepeatedPtrField();
}

void AppendEntriesArgs::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void AppendEntriesArgs::Clear() {
// @@protoc_insertion_point(message_clear_start:raftRpcProctoc.AppendEntriesArgs)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.entries_.Clear();
  ::memset(&_impl_.term_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.leadercommit_) -
      reinterpret_cast<char*>(&_impl_.term_)) + sizeof(_impl_.leadercommit_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* AppendEntriesArgs::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 Term = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.term_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 LeaderId = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.leaderid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 PrevLogIndex = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.prevlogindex_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 PrevLogTerm = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _impl_.prevlogterm_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .raftRpcProctoc.LogEntry Entries = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_entries(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else
          goto handle_unusual;
        continue;
      // int32 LeaderCommit = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          _impl_.leadercommit_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* AppendEntriesArgs::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:raftRpcProctoc.AppendEntriesArgs)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 Term = 1;
  if (this->_internal_term() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_term(), target);
  }

  // int32 LeaderId = 2;
  if (this->_internal_leaderid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(2, this->_internal_leaderid(), target);
  }

  // int32 PrevLogIndex = 3;
  if (this->_internal_prevlogindex() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(3, this->_internal_prevlogindex(), target);
  }

  // int32 PrevLogTerm = 4;
  if (this->_internal_prevlogterm() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(4, this->_internal_prevlogterm(), target);
  }

  // repeated .raftRpcProctoc.LogEntry Entries = 5;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_entries_size()); i < n; i++) {
    const auto& repfield = this->_internal_entries(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(5, repfield, repfield.GetCachedSize(), target, stream);
  }

  // int32 LeaderCommit = 6;
  if (this->_internal_leadercommit() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(6, this->_internal_leadercommit(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:raftRpcProctoc.AppendEntriesArgs)
  return target;
}

size_t AppendEntriesArgs::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:raftRpcProctoc.AppendEntriesArgs)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .raftRpcProctoc.LogEntry Entries = 5;
  total_size += 1UL * this->_internal_entries_size();
  for (const auto& msg : this->_impl_.entries_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // int32 Term = 1;
  if (this->_internal_term() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_term());
  }

  // int32 LeaderId = 2;
  if (this->_internal_leaderid() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_leaderid());
  }

  // int32 PrevLogIndex = 3;
  if (this->_internal_prevlogindex() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_prevlogindex());
  }

  // int32 PrevLogTerm = 4;
  if (this->_internal_prevlogterm() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_prevlogterm());
  }

  // int32 LeaderCommit = 6;
  if (this->_internal_leadercommit() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_leadercommit());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData AppendEntriesArgs::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    AppendEntriesArgs::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*AppendEntriesArgs::GetClassData() const { return &_class_data_; }


void AppendEntriesArgs::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<AppendEntriesArgs*>(&to_msg);
  auto& from = static_cast<const AppendEntriesArgs&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:raftRpcProctoc.AppendEntriesArgs)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.entries_.MergeFrom(from._impl_.entries_);
  if (from._internal_term() != 0) {
    _this->_internal_set_term(from._internal_term());
  }
  if (from._internal_leaderid() != 0) {
    _this->_internal_set_leaderid(from._internal_leaderid());
  }
  if (from._internal_prevlogindex() != 0) {
    _this->_internal_set_prevlogindex(from._internal_prevlogindex());
  }
  if (from._internal_prevlogterm() != 0) {
    _this->_internal_set_prevlogterm(from._internal_prevlogterm());
  }
  if (from._internal_leadercommit() != 0) {
    _this->_internal_set_leadercommit(from._internal_leadercommit());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void AppendEntriesArgs::CopyFrom(const AppendEntriesArgs& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:raftRpcProctoc.AppendEntriesArgs)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AppendEntriesArgs::IsInitialized() const {
  return true;
}

void AppendEntriesArgs::InternalSwap(AppendEntriesArgs* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.entries_.InternalSwap(&other->_impl_.entries_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(AppendEntriesArgs, _impl_.leadercommit_)
      + sizeof(AppendEntriesArgs::_impl_.leadercommit_)
      - PROTOBUF_FIELD_OFFSET(AppendEntriesArgs, _impl_.term_)>(
          reinterpret_cast<char*>(&_impl_.term_),
          reinterpret_cast<char*>(&other->_impl_.term_));
}

::PROTOBUF_NAMESPACE_ID::Metadata AppendEntriesArgs::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_raftRpcPro_2fraftRPC_2eproto_getter, &descriptor_table_raftRpcPro_2fraftRPC_2eproto_once,
      file_level_metadata_raftRpcPro_2fraftRPC_2eproto[2]);
}

// ===================================================================

class AppendEntriesReply::_Internal {
 public:
};

AppendEntriesReply::AppendEntriesReply(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:raftRpcProctoc.AppendEntriesReply)
}
AppendEntriesReply::AppendEntriesReply(const AppendEntriesReply& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  AppendEntriesReply* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.term_){}
    , decltype(_impl_.success_){}
    , decltype(_impl_.updatenextindex_){}
    , decltype(_impl_.appstate_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.term_, &from._impl_.term_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.appstate_) -
    reinterpret_cast<char*>(&_impl_.term_)) + sizeof(_impl_.appstate_));
  // @@protoc_insertion_point(copy_constructor:raftRpcProctoc.AppendEntriesReply)
}

inline void AppendEntriesReply::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.term_){0}
    , decltype(_impl_.success_){false}
    , decltype(_impl_.updatenextindex_){0}
    , decltype(_impl_.appstate_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

AppendEntriesReply::~AppendEntriesReply() {
  // @@protoc_insertion_point(destructor:raftRpcProctoc.AppendEntriesReply)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void AppendEntriesReply::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void AppendEntriesReply::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void AppendEntriesReply::Clear() {
// @@protoc_insertion_point(message_clear_start:raftRpcProctoc.AppendEntriesReply)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.term_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.appstate_) -
      reinterpret_cast<char*>(&_impl_.term_)) + sizeof(_impl_.appstate_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* AppendEntriesReply::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 Term = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.term_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool Success = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.success_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 UpdateNextIndex = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.updatenextindex_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 AppState = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _impl_.appstate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* AppendEntriesReply::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:raftRpcProctoc.AppendEntriesReply)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 Term = 1;
  if (this->_internal_term() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_term(), target);
  }

  // bool Success = 2;
  if (this->_internal_success() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(2, this->_internal_success(), target);
  }

  // int32 UpdateNextIndex = 3;
  if (this->_internal_updatenextindex() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(3, this->_internal_updatenextindex(), target);
  }

  // int32 AppState = 4;
  if (this->_internal_appstate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(4, this->_internal_appstate(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:raftRpcProctoc.AppendEntriesReply)
  return target;
}

size_t AppendEntriesReply::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:raftRpcProctoc.AppendEntriesReply)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 Term = 1;
  if (this->_internal_term() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_term());
  }

  // bool Success = 2;
  if (this->_internal_success() != 0) {
    total_size += 1 + 1;
  }

  // int32 UpdateNextIndex = 3;
  if (this->_internal_updatenextindex() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_updatenextindex());
  }

  // int32 AppState = 4;
  if (this->_internal_appstate() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_appstate());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData AppendEntriesReply::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    AppendEntriesReply::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*AppendEntriesReply::GetClassData() const { return &_class_data_; }


void AppendEntriesReply::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<AppendEntriesReply*>(&to_msg);
  auto& from = static_cast<const AppendEntriesReply&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:raftRpcProctoc.AppendEntriesReply)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_term() != 0) {
    _this->_internal_set_term(from._internal_term());
  }
  if (from._internal_success() != 0) {
    _this->_internal_set_success(from._internal_success());
  }
  if (from._internal_updatenextindex() != 0) {
    _this->_internal_set_updatenextindex(from._internal_updatenextindex());
  }
  if (from._internal_appstate() != 0) {
    _this->_internal_set_appstate(from._internal_appstate());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void AppendEntriesReply::CopyFrom(const AppendEntriesReply& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:raftRpcProctoc.AppendEntriesReply)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AppendEntriesReply::IsInitialized() const {
  return true;
}

void AppendEntriesReply::InternalSwap(AppendEntriesReply* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(AppendEntriesReply, _impl_.appstate_)
      + sizeof(AppendEntriesReply::_impl_.appstate_)
      - PROTOBUF_FIELD_OFFSET(AppendEntriesReply, _impl_.term_)>(
          reinterpret_cast<char*>(&_impl_.term_),
          reinterpret_cast<char*>(&other->_impl_.term_));
}

::PROTOBUF_NAMESPACE_ID::Metadata AppendEntriesReply::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_raftRpcPro_2fraftRPC_2eproto_getter, &descriptor_table_raftRpcPro_2fraftRPC_2eproto_once,
      file_level_metadata_raftRpcPro_2fraftRPC_2eproto[3]);
}

// ===================================================================

class RequestVoteArgs::_Internal {
 public:
};

RequestVoteArgs::RequestVoteArgs(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:raftRpcProctoc.RequestVoteArgs)
}
RequestVoteArgs::RequestVoteArgs(const RequestVoteArgs& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  RequestVoteArgs* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.term_){}
    , decltype(_impl_.candidateid_){}
    , decltype(_impl_.lastlogindex_){}
    , decltype(_impl_.lastlogterm_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.term_, &from._impl_.term_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.lastlogterm_) -
    reinterpret_cast<char*>(&_impl_.term_)) + sizeof(_impl_.lastlogterm_));
  // @@protoc_insertion_point(copy_constructor:raftRpcProctoc.RequestVoteArgs)
}

inline void RequestVoteArgs::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.term_){0}
    , decltype(_impl_.candidateid_){0}
    , decltype(_impl_.lastlogindex_){0}
    , decltype(_impl_.lastlogterm_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

RequestVoteArgs::~RequestVoteArgs() {
  // @@protoc_insertion_point(destructor:raftRpcProctoc.RequestVoteArgs)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void RequestVoteArgs::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void RequestVoteArgs::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void RequestVoteArgs::Clear() {
// @@protoc_insertion_point(message_clear_start:raftRpcProctoc.RequestVoteArgs)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.term_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.lastlogterm_) -
      reinterpret_cast<char*>(&_impl_.term_)) + sizeof(_impl_.lastlogterm_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RequestVoteArgs::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 Term = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.term_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 CandidateId = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.candidateid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 LastLogIndex = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.lastlogindex_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 LastLogTerm = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _impl_.lastlogterm_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RequestVoteArgs::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:raftRpcProctoc.RequestVoteArgs)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 Term = 1;
  if (this->_internal_term() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_term(), target);
  }

  // int32 CandidateId = 2;
  if (this->_internal_candidateid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(2, this->_internal_candidateid(), target);
  }

  // int32 LastLogIndex = 3;
  if (this->_internal_lastlogindex() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(3, this->_internal_lastlogindex(), target);
  }

  // int32 LastLogTerm = 4;
  if (this->_internal_lastlogterm() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(4, this->_internal_lastlogterm(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:raftRpcProctoc.RequestVoteArgs)
  return target;
}

size_t RequestVoteArgs::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:raftRpcProctoc.RequestVoteArgs)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 Term = 1;
  if (this->_internal_term() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_term());
  }

  // int32 CandidateId = 2;
  if (this->_internal_candidateid() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_candidateid());
  }

  // int32 LastLogIndex = 3;
  if (this->_internal_lastlogindex() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_lastlogindex());
  }

  // int32 LastLogTerm = 4;
  if (this->_internal_lastlogterm() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_lastlogterm());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RequestVoteArgs::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    RequestVoteArgs::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RequestVoteArgs::GetClassData() const { return &_class_data_; }


void RequestVoteArgs::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<RequestVoteArgs*>(&to_msg);
  auto& from = static_cast<const RequestVoteArgs&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:raftRpcProctoc.RequestVoteArgs)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_term() != 0) {
    _this->_internal_set_term(from._internal_term());
  }
  if (from._internal_candidateid() != 0) {
    _this->_internal_set_candidateid(from._internal_candidateid());
  }
  if (from._internal_lastlogindex() != 0) {
    _this->_internal_set_lastlogindex(from._internal_lastlogindex());
  }
  if (from._internal_lastlogterm() != 0) {
    _this->_internal_set_lastlogterm(from._internal_lastlogterm());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RequestVoteArgs::CopyFrom(const RequestVoteArgs& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:raftRpcProctoc.RequestVoteArgs)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RequestVoteArgs::IsInitialized() const {
  return true;
}

void RequestVoteArgs::InternalSwap(RequestVoteArgs* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RequestVoteArgs, _impl_.lastlogterm_)
      + sizeof(RequestVoteArgs::_impl_.lastlogterm_)
      - PROTOBUF_FIELD_OFFSET(RequestVoteArgs, _impl_.term_)>(
          reinterpret_cast<char*>(&_impl_.term_),
          reinterpret_cast<char*>(&other->_impl_.term_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RequestVoteArgs::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_raftRpcPro_2fraftRPC_2eproto_getter, &descriptor_table_raftRpcPro_2fraftRPC_2eproto_once,
      file_level_metadata_raftRpcPro_2fraftRPC_2eproto[4]);
}

// ===================================================================

class RequestVoteReply::_Internal {
 public:
};

RequestVoteReply::RequestVoteReply(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:raftRpcProctoc.RequestVoteReply)
}
RequestVoteReply::RequestVoteReply(const RequestVoteReply& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  RequestVoteReply* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.term_){}
    , decltype(_impl_.votegranted_){}
    , decltype(_impl_.votestate_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.term_, &from._impl_.term_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.votestate_) -
    reinterpret_cast<char*>(&_impl_.term_)) + sizeof(_impl_.votestate_));
  // @@protoc_insertion_point(copy_constructor:raftRpcProctoc.RequestVoteReply)
}

inline void RequestVoteReply::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.term_){0}
    , decltype(_impl_.votegranted_){false}
    , decltype(_impl_.votestate_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

RequestVoteReply::~RequestVoteReply() {
  // @@protoc_insertion_point(destructor:raftRpcProctoc.RequestVoteReply)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void RequestVoteReply::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void RequestVoteReply::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void RequestVoteReply::Clear() {
// @@protoc_insertion_point(message_clear_start:raftRpcProctoc.RequestVoteReply)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.term_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.votestate_) -
      reinterpret_cast<char*>(&_impl_.term_)) + sizeof(_impl_.votestate_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RequestVoteReply::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 Term = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.term_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool VoteGranted = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.votegranted_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 VoteState = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.votestate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RequestVoteReply::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:raftRpcProctoc.RequestVoteReply)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 Term = 1;
  if (this->_internal_term() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_term(), target);
  }

  // bool VoteGranted = 2;
  if (this->_internal_votegranted() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(2, this->_internal_votegranted(), target);
  }

  // int32 VoteState = 3;
  if (this->_internal_votestate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(3, this->_internal_votestate(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:raftRpcProctoc.RequestVoteReply)
  return target;
}

size_t RequestVoteReply::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:raftRpcProctoc.RequestVoteReply)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 Term = 1;
  if (this->_internal_term() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_term());
  }

  // bool VoteGranted = 2;
  if (this->_internal_votegranted() != 0) {
    total_size += 1 + 1;
  }

  // int32 VoteState = 3;
  if (this->_internal_votestate() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_votestate());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RequestVoteReply::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    RequestVoteReply::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RequestVoteReply::GetClassData() const { return &_class_data_; }


void RequestVoteReply::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<RequestVoteReply*>(&to_msg);
  auto& from = static_cast<const RequestVoteReply&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:raftRpcProctoc.RequestVoteReply)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_term() != 0) {
    _this->_internal_set_term(from._internal_term());
  }
  if (from._internal_votegranted() != 0) {
    _this->_internal_set_votegranted(from._internal_votegranted());
  }
  if (from._internal_votestate() != 0) {
    _this->_internal_set_votestate(from._internal_votestate());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RequestVoteReply::CopyFrom(const RequestVoteReply& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:raftRpcProctoc.RequestVoteReply)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RequestVoteReply::IsInitialized() const {
  return true;
}

void RequestVoteReply::InternalSwap(RequestVoteReply* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RequestVoteReply, _impl_.votestate_)
      + sizeof(RequestVoteReply::_impl_.votestate_)
      - PROTOBUF_FIELD_OFFSET(RequestVoteReply, _impl_.term_)>(
          reinterpret_cast<char*>(&_impl_.term_),
          reinterpret_cast<char*>(&other->_impl_.term_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RequestVoteReply::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_raftRpcPro_2fraftRPC_2eproto_getter, &descriptor_table_raftRpcPro_2fraftRPC_2eproto_once,
      file_level_metadata_raftRpcPro_2fraftRPC_2eproto[5]);
}

// ===================================================================

class InstallSnapshotRequest::_Internal {
 public:
};

InstallSnapshotRequest::InstallSnapshotRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:raftRpcProctoc.InstallSnapshotRequest)
}
InstallSnapshotRequest::InstallSnapshotRequest(const InstallSnapshotRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  InstallSnapshotRequest* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.data_){}
    , decltype(_impl_.leaderid_){}
    , decltype(_impl_.term_){}
    , decltype(_impl_.lastsnapshotincludeindex_){}
    , decltype(_impl_.lastsnapshotincludeterm_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.data_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.data_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_data().empty()) {
    _this->_impl_.data_.Set(from._internal_data(), 
      _this->GetArenaForAllocation());
  }
  ::memcpy(&_impl_.leaderid_, &from._impl_.leaderid_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.lastsnapshotincludeterm_) -
    reinterpret_cast<char*>(&_impl_.leaderid_)) + sizeof(_impl_.lastsnapshotincludeterm_));
  // @@protoc_insertion_point(copy_constructor:raftRpcProctoc.InstallSnapshotRequest)
}

inline void InstallSnapshotRequest::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.data_){}
    , decltype(_impl_.leaderid_){0}
    , decltype(_impl_.term_){0}
    , decltype(_impl_.lastsnapshotincludeindex_){0}
    , decltype(_impl_.lastsnapshotincludeterm_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.data_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.data_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

InstallSnapshotRequest::~InstallSnapshotRequest() {
  // @@protoc_insertion_point(destructor:raftRpcProctoc.InstallSnapshotRequest)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void InstallSnapshotRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.data_.Destroy();
}

void InstallSnapshotRequest::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void InstallSnapshotRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:raftRpcProctoc.InstallSnapshotRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.data_.ClearToEmpty();
  ::memset(&_impl_.leaderid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.lastsnapshotincludeterm_) -
      reinterpret_cast<char*>(&_impl_.leaderid_)) + sizeof(_impl_.lastsnapshotincludeterm_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* InstallSnapshotRequest::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 LeaderId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.leaderid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 Term = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.term_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 LastSnapShotIncludeIndex = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.lastsnapshotincludeindex_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 LastSnapShotIncludeTerm = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _impl_.lastsnapshotincludeterm_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bytes Data = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_data();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* InstallSnapshotRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:raftRpcProctoc.InstallSnapshotRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 LeaderId = 1;
  if (this->_internal_leaderid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_leaderid(), target);
  }

  // int32 Term = 2;
  if (this->_internal_term() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(2, this->_internal_term(), target);
  }

  // int32 LastSnapShotIncludeIndex = 3;
  if (this->_internal_lastsnapshotincludeindex() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(3, this->_internal_lastsnapshotincludeindex(), target);
  }

  // int32 LastSnapShotIncludeTerm = 4;
  if (this->_internal_lastsnapshotincludeterm() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(4, this->_internal_lastsnapshotincludeterm(), target);
  }

  // bytes Data = 5;
  if (!this->_internal_data().empty()) {
    target = stream->WriteBytesMaybeAliased(
        5, this->_internal_data(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:raftRpcProctoc.InstallSnapshotRequest)
  return target;
}

size_t InstallSnapshotRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:raftRpcProctoc.InstallSnapshotRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes Data = 5;
  if (!this->_internal_data().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_data());
  }

  // int32 LeaderId = 1;
  if (this->_internal_leaderid() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_leaderid());
  }

  // int32 Term = 2;
  if (this->_internal_term() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_term());
  }

  // int32 LastSnapShotIncludeIndex = 3;
  if (this->_internal_lastsnapshotincludeindex() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_lastsnapshotincludeindex());
  }

  // int32 LastSnapShotIncludeTerm = 4;
  if (this->_internal_lastsnapshotincludeterm() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_lastsnapshotincludeterm());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData InstallSnapshotRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    InstallSnapshotRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*InstallSnapshotRequest::GetClassData() const { return &_class_data_; }


void InstallSnapshotRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<InstallSnapshotRequest*>(&to_msg);
  auto& from = static_cast<const InstallSnapshotRequest&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:raftRpcProctoc.InstallSnapshotRequest)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_data().empty()) {
    _this->_internal_set_data(from._internal_data());
  }
  if (from._internal_leaderid() != 0) {
    _this->_internal_set_leaderid(from._internal_leaderid());
  }
  if (from._internal_term() != 0) {
    _this->_internal_set_term(from._internal_term());
  }
  if (from._internal_lastsnapshotincludeindex() != 0) {
    _this->_internal_set_lastsnapshotincludeindex(from._internal_lastsnapshotincludeindex());
  }
  if (from._internal_lastsnapshotincludeterm() != 0) {
    _this->_internal_set_lastsnapshotincludeterm(from._internal_lastsnapshotincludeterm());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void InstallSnapshotRequest::CopyFrom(const InstallSnapshotRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:raftRpcProctoc.InstallSnapshotRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool InstallSnapshotRequest::IsInitialized() const {
  return true;
}

void InstallSnapshotRequest::InternalSwap(InstallSnapshotRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.data_, lhs_arena,
      &other->_impl_.data_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(InstallSnapshotRequest, _impl_.lastsnapshotincludeterm_)
      + sizeof(InstallSnapshotRequest::_impl_.lastsnapshotincludeterm_)
      - PROTOBUF_FIELD_OFFSET(InstallSnapshotRequest, _impl_.leaderid_)>(
          reinterpret_cast<char*>(&_impl_.leaderid_),
          reinterpret_cast<char*>(&other->_impl_.leaderid_));
}

::PROTOBUF_NAMESPACE_ID::Metadata InstallSnapshotRequest::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_raftRpcPro_2fraftRPC_2eproto_getter, &descriptor_table_raftRpcPro_2fraftRPC_2eproto_once,
      file_level_metadata_raftRpcPro_2fraftRPC_2eproto[6]);
}

// ===================================================================

class InstallSnapshotResponse::_Internal {
 public:
};

InstallSnapshotResponse::InstallSnapshotResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:raftRpcProctoc.InstallSnapshotResponse)
}
InstallSnapshotResponse::InstallSnapshotResponse(const InstallSnapshotResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  InstallSnapshotResponse* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.term_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _this->_impl_.term_ = from._impl_.term_;
  // @@protoc_insertion_point(copy_constructor:raftRpcProctoc.InstallSnapshotResponse)
}

inline void InstallSnapshotResponse::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.term_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

InstallSnapshotResponse::~InstallSnapshotResponse() {
  // @@protoc_insertion_point(destructor:raftRpcProctoc.InstallSnapshotResponse)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void InstallSnapshotResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void InstallSnapshotResponse::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void InstallSnapshotResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:raftRpcProctoc.InstallSnapshotResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.term_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* InstallSnapshotResponse::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 Term = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.term_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* InstallSnapshotResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:raftRpcProctoc.InstallSnapshotResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 Term = 1;
  if (this->_internal_term() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_term(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:raftRpcProctoc.InstallSnapshotResponse)
  return target;
}

size_t InstallSnapshotResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:raftRpcProctoc.InstallSnapshotResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 Term = 1;
  if (this->_internal_term() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_term());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData InstallSnapshotResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    InstallSnapshotResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*InstallSnapshotResponse::GetClassData() const { return &_class_data_; }


void InstallSnapshotResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<InstallSnapshotResponse*>(&to_msg);
  auto& from = static_cast<const InstallSnapshotResponse&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:raftRpcProctoc.InstallSnapshotResponse)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_term() != 0) {
    _this->_internal_set_term(from._internal_term());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void InstallSnapshotResponse::CopyFrom(const InstallSnapshotResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:raftRpcProctoc.InstallSnapshotResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool InstallSnapshotResponse::IsInitialized() const {
  return true;
}

void InstallSnapshotResponse::InternalSwap(InstallSnapshotResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_.term_, other->_impl_.term_);
}

::PROTOBUF_NAMESPACE_ID::Metadata InstallSnapshotResponse::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_raftRpcPro_2fraftRPC_2eproto_getter, &descriptor_table_raftRpcPro_2fraftRPC_2eproto_once,
      file_level_metadata_raftRpcPro_2fraftRPC_2eproto[7]);
}

// ===================================================================

class ChangeConfigArgs::_Internal {
 public:
};

ChangeConfigArgs::ChangeConfigArgs(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:raftRpcProctoc.ChangeConfigArgs)
}
ChangeConfigArgs::ChangeConfigArgs(const ChangeConfigArgs& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  ChangeConfigArgs* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.nodeid_){}
    , decltype(_impl_.address_){}
    , decltype(_impl_.type_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.nodeid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.nodeid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_nodeid().empty()) {
    _this->_impl_.nodeid_.Set(from._internal_nodeid(), 
      _this->GetArenaForAllocation());
  }
  _impl_.address_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.address_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_address().empty()) {
    _this->_impl_.address_.Set(from._internal_address(), 
      _this->GetArenaForAllocation());
  }
  _this->_impl_.type_ = from._impl_.type_;
  // @@protoc_insertion_point(copy_constructor:raftRpcProctoc.ChangeConfigArgs)
}

inline void ChangeConfigArgs::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.nodeid_){}
    , decltype(_impl_.address_){}
    , decltype(_impl_.type_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.nodeid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.nodeid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.address_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.address_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

ChangeConfigArgs::~ChangeConfigArgs() {
  // @@protoc_insertion_point(destructor:raftRpcProctoc.ChangeConfigArgs)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void ChangeConfigArgs::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.nodeid_.Destroy();
  _impl_.address_.Destroy();
}

void ChangeConfigArgs::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void ChangeConfigArgs::Clear() {
// @@protoc_insertion_point(message_clear_start:raftRpcProctoc.ChangeConfigArgs)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.nodeid_.ClearToEmpty();
  _impl_.address_.ClearToEmpty();
  _impl_.type_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ChangeConfigArgs::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .raftRpcProctoc.ConfigChangeType Type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_type(static_cast<::raftRpcProctoc::ConfigChangeType>(val));
        } else
          goto handle_unusual;
        continue;
      // string NodeId = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_nodeid();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "raftRpcProctoc.ChangeConfigArgs.NodeId"));
        } else
          goto handle_unusual;
        continue;
      // string Address = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_address();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "raftRpcProctoc.ChangeConfigArgs.Address"));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ChangeConfigArgs::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:raftRpcProctoc.ChangeConfigArgs)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .raftRpcProctoc.ConfigChangeType Type = 1;
  if (this->_internal_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      1, this->_internal_type(), target);
  }

  // string NodeId = 2;
  if (!this->_internal_nodeid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_nodeid().data(), static_cast<int>(this->_internal_nodeid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "raftRpcProctoc.ChangeConfigArgs.NodeId");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_nodeid(), target);
  }

  // string Address = 3;
  if (!this->_internal_address().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_address().data(), static_cast<int>(this->_internal_address().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "raftRpcProctoc.ChangeConfigArgs.Address");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_address(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:raftRpcProctoc.ChangeConfigArgs)
  return target;
}

size_t ChangeConfigArgs::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:raftRpcProctoc.ChangeConfigArgs)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string NodeId = 2;
  if (!this->_internal_nodeid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_nodeid());
  }

  // string Address = 3;
  if (!this->_internal_address().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_address());
  }

  // .raftRpcProctoc.ConfigChangeType Type = 1;
  if (this->_internal_type() != 0) {
    total_size += 1 +
      ::_pbi::WireFormatLite::EnumSize(this->_internal_type());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ChangeConfigArgs::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    ChangeConfigArgs::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ChangeConfigArgs::GetClassData() const { return &_class_data_; }


void ChangeConfigArgs::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<ChangeConfigArgs*>(&to_msg);
  auto& from = static_cast<const ChangeConfigArgs&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:raftRpcProctoc.ChangeConfigArgs)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_nodeid().empty()) {
    _this->_internal_set_nodeid(from._internal_nodeid());
  }
  if (!from._internal_address().empty()) {
    _this->_internal_set_address(from._internal_address());
  }
  if (from._internal_type() != 0) {
    _this->_internal_set_type(from._internal_type());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ChangeConfigArgs::CopyFrom(const ChangeConfigArgs& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:raftRpcProctoc.ChangeConfigArgs)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ChangeConfigArgs::IsInitialized() const {
  return true;
}

void ChangeConfigArgs::InternalSwap(ChangeConfigArgs* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.nodeid_, lhs_arena,
      &other->_impl_.nodeid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.address_, lhs_arena,
      &other->_impl_.address_, rhs_arena
  );
  swap(_impl_.type_, other->_impl_.type_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ChangeConfigArgs::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_raftRpcPro_2fraftRPC_2eproto_getter, &descriptor_table_raftRpcPro_2fraftRPC_2eproto_once,
      file_level_metadata_raftRpcPro_2fraftRPC_2eproto[8]);
}

// ===================================================================

class ChangeConfigReply::_Internal {
 public:
};

ChangeConfigReply::ChangeConfigReply(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:raftRpcProctoc.ChangeConfigReply)
}
ChangeConfigReply::ChangeConfigReply(const ChangeConfigReply& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  ChangeConfigReply* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.error_){}
    , decltype(_impl_.success_){}
    , decltype(_impl_.isleader_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.error_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.error_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_error().empty()) {
    _this->_impl_.error_.Set(from._internal_error(), 
      _this->GetArenaForAllocation());
  }
  ::memcpy(&_impl_.success_, &from._impl_.success_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.isleader_) -
    reinterpret_cast<char*>(&_impl_.success_)) + sizeof(_impl_.isleader_));
  // @@protoc_insertion_point(copy_constructor:raftRpcProctoc.ChangeConfigReply)
}

inline void ChangeConfigReply::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.error_){}
    , decltype(_impl_.success_){false}
    , decltype(_impl_.isleader_){false}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.error_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.error_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

ChangeConfigReply::~ChangeConfigReply() {
  // @@protoc_insertion_point(destructor:raftRpcProctoc.ChangeConfigReply)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void ChangeConfigReply::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.error_.Destroy();
}

void ChangeConfigReply::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void ChangeConfigReply::Clear() {
// @@protoc_insertion_point(message_clear_start:raftRpcProctoc.ChangeConfigReply)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.error_.ClearToEmpty();
  ::memset(&_impl_.success_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.isleader_) -
      reinterpret_cast<char*>(&_impl_.success_)) + sizeof(_impl_.isleader_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ChangeConfigReply::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool Success = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.success_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string Error = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_error();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "raftRpcProctoc.ChangeConfigReply.Error"));
        } else
          goto handle_unusual;
        continue;
      // bool IsLeader = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.isleader_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ChangeConfigReply::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:raftRpcProctoc.ChangeConfigReply)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool Success = 1;
  if (this->_internal_success() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(1, this->_internal_success(), target);
  }

  // string Error = 2;
  if (!this->_internal_error().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_error().data(), static_cast<int>(this->_internal_error().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "raftRpcProctoc.ChangeConfigReply.Error");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_error(), target);
  }

  // bool IsLeader = 3;
  if (this->_internal_isleader() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(3, this->_internal_isleader(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:raftRpcProctoc.ChangeConfigReply)
  return target;
}

size_t ChangeConfigReply::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:raftRpcProctoc.ChangeConfigReply)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string Error = 2;
  if (!this->_internal_error().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_error());
  }

  // bool Success = 1;
  if (this->_internal_success() != 0) {
    total_size += 1 + 1;
  }

  // bool IsLeader = 3;
  if (this->_internal_isleader() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ChangeConfigReply::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    ChangeConfigReply::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ChangeConfigReply::GetClassData() const { return &_class_data_; }


void ChangeConfigReply::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<ChangeConfigReply*>(&to_msg);
  auto& from = static_cast<const ChangeConfigReply&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:raftRpcProctoc.ChangeConfigReply)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_error().empty()) {
    _this->_internal_set_error(from._internal_error());
  }
  if (from._internal_success() != 0) {
    _this->_internal_set_success(from._internal_success());
  }
  if (from._internal_isleader() != 0) {
    _this->_internal_set_isleader(from._internal_isleader());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ChangeConfigReply::CopyFrom(const ChangeConfigReply& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:raftRpcProctoc.ChangeConfigReply)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ChangeConfigReply::IsInitialized() const {
  return true;
}

void ChangeConfigReply::InternalSwap(ChangeConfigReply* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.error_, lhs_arena,
      &other->_impl_.error_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ChangeConfigReply, _impl_.isleader_)
      + sizeof(ChangeConfigReply::_impl_.isleader_)
      - PROTOBUF_FIELD_OFFSET(ChangeConfigReply, _impl_.success_)>(
          reinterpret_cast<char*>(&_impl_.success_),
          reinterpret_cast<char*>(&other->_impl_.success_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ChangeConfigReply::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_raftRpcPro_2fraftRPC_2eproto_getter, &descriptor_table_raftRpcPro_2fraftRPC_2eproto_once,
      file_level_metadata_raftRpcPro_2fraftRPC_2eproto[9]);
}

// ===================================================================

raftRpc::~raftRpc() {}

const ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor* raftRpc::descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_raftRpcPro_2fraftRPC_2eproto);
  return file_level_service_descriptors_raftRpcPro_2fraftRPC_2eproto[0];
}

const ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor* raftRpc::GetDescriptor() {
  return descriptor();
}

void raftRpc::AppendEntries(::PROTOBUF_NAMESPACE_ID::RpcController* controller,
                         const ::raftRpcProctoc::AppendEntriesArgs*,
                         ::raftRpcProctoc::AppendEntriesReply*,
                         ::google::protobuf::Closure* done) {
  controller->SetFailed("Method AppendEntries() not implemented.");
  done->Run();
}

void raftRpc::InstallSnapshot(::PROTOBUF_NAMESPACE_ID::RpcController* controller,
                         const ::raftRpcProctoc::InstallSnapshotRequest*,
                         ::raftRpcProctoc::InstallSnapshotResponse*,
                         ::google::protobuf::Closure* done) {
  controller->SetFailed("Method InstallSnapshot() not implemented.");
  done->Run();
}

void raftRpc::RequestVote(::PROTOBUF_NAMESPACE_ID::RpcController* controller,
                         const ::raftRpcProctoc::RequestVoteArgs*,
                         ::raftRpcProctoc::RequestVoteReply*,
                         ::google::protobuf::Closure* done) {
  controller->SetFailed("Method RequestVote() not implemented.");
  done->Run();
}

void raftRpc::ChangeConfig(::PROTOBUF_NAMESPACE_ID::RpcController* controller,
                         const ::raftRpcProctoc::ChangeConfigArgs*,
                         ::raftRpcProctoc::ChangeConfigReply*,
                         ::google::protobuf::Closure* done) {
  controller->SetFailed("Method ChangeConfig() not implemented.");
  done->Run();
}

void raftRpc::CallMethod(const ::PROTOBUF_NAMESPACE_ID::MethodDescriptor* method,
                             ::PROTOBUF_NAMESPACE_ID::RpcController* controller,
                             const ::PROTOBUF_NAMESPACE_ID::Message* request,
                             ::PROTOBUF_NAMESPACE_ID::Message* response,
                             ::google::protobuf::Closure* done) {
  GOOGLE_DCHECK_EQ(method->service(), file_level_service_descriptors_raftRpcPro_2fraftRPC_2eproto[0]);
  switch(method->index()) {
    case 0:
      AppendEntries(controller,
             ::PROTOBUF_NAMESPACE_ID::internal::DownCast<const ::raftRpcProctoc::AppendEntriesArgs*>(
                 request),
             ::PROTOBUF_NAMESPACE_ID::internal::DownCast<::raftRpcProctoc::AppendEntriesReply*>(
                 response),
             done);
      break;
    case 1:
      InstallSnapshot(controller,
             ::PROTOBUF_NAMESPACE_ID::internal::DownCast<const ::raftRpcProctoc::InstallSnapshotRequest*>(
                 request),
             ::PROTOBUF_NAMESPACE_ID::internal::DownCast<::raftRpcProctoc::InstallSnapshotResponse*>(
                 response),
             done);
      break;
    case 2:
      RequestVote(controller,
             ::PROTOBUF_NAMESPACE_ID::internal::DownCast<const ::raftRpcProctoc::RequestVoteArgs*>(
                 request),
             ::PROTOBUF_NAMESPACE_ID::internal::DownCast<::raftRpcProctoc::RequestVoteReply*>(
                 response),
             done);
      break;
    case 3:
      ChangeConfig(controller,
             ::PROTOBUF_NAMESPACE_ID::internal::DownCast<const ::raftRpcProctoc::ChangeConfigArgs*>(
                 request),
             ::PROTOBUF_NAMESPACE_ID::internal::DownCast<::raftRpcProctoc::ChangeConfigReply*>(
                 response),
             done);
      break;
    default:
      GOOGLE_LOG(FATAL) << "Bad method index; this should never happen.";
      break;
  }
}

const ::PROTOBUF_NAMESPACE_ID::Message& raftRpc::GetRequestPrototype(
    const ::PROTOBUF_NAMESPACE_ID::MethodDescriptor* method) const {
  GOOGLE_DCHECK_EQ(method->service(), descriptor());
  switch(method->index()) {
    case 0:
      return ::raftRpcProctoc::AppendEntriesArgs::default_instance();
    case 1:
      return ::raftRpcProctoc::InstallSnapshotRequest::default_instance();
    case 2:
      return ::raftRpcProctoc::RequestVoteArgs::default_instance();
    case 3:
      return ::raftRpcProctoc::ChangeConfigArgs::default_instance();
    default:
      GOOGLE_LOG(FATAL) << "Bad method index; this should never happen.";
      return *::PROTOBUF_NAMESPACE_ID::MessageFactory::generated_factory()
          ->GetPrototype(method->input_type());
  }
}

const ::PROTOBUF_NAMESPACE_ID::Message& raftRpc::GetResponsePrototype(
    const ::PROTOBUF_NAMESPACE_ID::MethodDescriptor* method) const {
  GOOGLE_DCHECK_EQ(method->service(), descriptor());
  switch(method->index()) {
    case 0:
      return ::raftRpcProctoc::AppendEntriesReply::default_instance();
    case 1:
      return ::raftRpcProctoc::InstallSnapshotResponse::default_instance();
    case 2:
      return ::raftRpcProctoc::RequestVoteReply::default_instance();
    case 3:
      return ::raftRpcProctoc::ChangeConfigReply::default_instance();
    default:
      GOOGLE_LOG(FATAL) << "Bad method index; this should never happen.";
      return *::PROTOBUF_NAMESPACE_ID::MessageFactory::generated_factory()
          ->GetPrototype(method->output_type());
  }
}

raftRpc_Stub::raftRpc_Stub(::PROTOBUF_NAMESPACE_ID::RpcChannel* channel)
  : channel_(channel), owns_channel_(false) {}
raftRpc_Stub::raftRpc_Stub(
    ::PROTOBUF_NAMESPACE_ID::RpcChannel* channel,
    ::PROTOBUF_NAMESPACE_ID::Service::ChannelOwnership ownership)
  : channel_(channel),
    owns_channel_(ownership == ::PROTOBUF_NAMESPACE_ID::Service::STUB_OWNS_CHANNEL) {}
raftRpc_Stub::~raftRpc_Stub() {
  if (owns_channel_) delete channel_;
}

void raftRpc_Stub::AppendEntries(::PROTOBUF_NAMESPACE_ID::RpcController* controller,
                              const ::raftRpcProctoc::AppendEntriesArgs* request,
                              ::raftRpcProctoc::AppendEntriesReply* response,
                              ::google::protobuf::Closure* done) {
  channel_->CallMethod(descriptor()->method(0),
                       controller, request, response, done);
}
void raftRpc_Stub::InstallSnapshot(::PROTOBUF_NAMESPACE_ID::RpcController* controller,
                              const ::raftRpcProctoc::InstallSnapshotRequest* request,
                              ::raftRpcProctoc::InstallSnapshotResponse* response,
                              ::google::protobuf::Closure* done) {
  channel_->CallMethod(descriptor()->method(1),
                       controller, request, response, done);
}
void raftRpc_Stub::RequestVote(::PROTOBUF_NAMESPACE_ID::RpcController* controller,
                              const ::raftRpcProctoc::RequestVoteArgs* request,
                              ::raftRpcProctoc::RequestVoteReply* response,
                              ::google::protobuf::Closure* done) {
  channel_->CallMethod(descriptor()->method(2),
                       controller, request, response, done);
}
void raftRpc_Stub::ChangeConfig(::PROTOBUF_NAMESPACE_ID::RpcController* controller,
                              const ::raftRpcProctoc::ChangeConfigArgs* request,
                              ::raftRpcProctoc::ChangeConfigReply* response,
                              ::google::protobuf::Closure* done) {
  channel_->CallMethod(descriptor()->method(3),
                       controller, request, response, done);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace raftRpcProctoc
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::raftRpcProctoc::ConfigChange*
Arena::CreateMaybeMessage< ::raftRpcProctoc::ConfigChange >(Arena* arena) {
  return Arena::CreateMessageInternal< ::raftRpcProctoc::ConfigChange >(arena);
}
template<> PROTOBUF_NOINLINE ::raftRpcProctoc::LogEntry*
Arena::CreateMaybeMessage< ::raftRpcProctoc::LogEntry >(Arena* arena) {
  return Arena::CreateMessageInternal< ::raftRpcProctoc::LogEntry >(arena);
}
template<> PROTOBUF_NOINLINE ::raftRpcProctoc::AppendEntriesArgs*
Arena::CreateMaybeMessage< ::raftRpcProctoc::AppendEntriesArgs >(Arena* arena) {
  return Arena::CreateMessageInternal< ::raftRpcProctoc::AppendEntriesArgs >(arena);
}
template<> PROTOBUF_NOINLINE ::raftRpcProctoc::AppendEntriesReply*
Arena::CreateMaybeMessage< ::raftRpcProctoc::AppendEntriesReply >(Arena* arena) {
  return Arena::CreateMessageInternal< ::raftRpcProctoc::AppendEntriesReply >(arena);
}
template<> PROTOBUF_NOINLINE ::raftRpcProctoc::RequestVoteArgs*
Arena::CreateMaybeMessage< ::raftRpcProctoc::RequestVoteArgs >(Arena* arena) {
  return Arena::CreateMessageInternal< ::raftRpcProctoc::RequestVoteArgs >(arena);
}
template<> PROTOBUF_NOINLINE ::raftRpcProctoc::RequestVoteReply*
Arena::CreateMaybeMessage< ::raftRpcProctoc::RequestVoteReply >(Arena* arena) {
  return Arena::CreateMessageInternal< ::raftRpcProctoc::RequestVoteReply >(arena);
}
template<> PROTOBUF_NOINLINE ::raftRpcProctoc::InstallSnapshotRequest*
Arena::CreateMaybeMessage< ::raftRpcProctoc::InstallSnapshotRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::raftRpcProctoc::InstallSnapshotRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::raftRpcProctoc::InstallSnapshotResponse*
Arena::CreateMaybeMessage< ::raftRpcProctoc::InstallSnapshotResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::raftRpcProctoc::InstallSnapshotResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::raftRpcProctoc::ChangeConfigArgs*
Arena::CreateMaybeMessage< ::raftRpcProctoc::ChangeConfigArgs >(Arena* arena) {
  return Arena::CreateMessageInternal< ::raftRpcProctoc::ChangeConfigArgs >(arena);
}
template<> PROTOBUF_NOINLINE ::raftRpcProctoc::ChangeConfigReply*
Arena::CreateMaybeMessage< ::raftRpcProctoc::ChangeConfigReply >(Arena* arena) {
  return Arena::CreateMessageInternal< ::raftRpcProctoc::ChangeConfigReply >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
