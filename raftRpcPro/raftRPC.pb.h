// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: raftRpcPro/raftRPC.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_raftRpcPro_2fraftRPC_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_raftRpcPro_2fraftRPC_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021012 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/service.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_raftRpcPro_2fraftRPC_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_raftRpcPro_2fraftRPC_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_raftRpcPro_2fraftRPC_2eproto;
namespace raftRpcProctoc {
class AppendEntriesArgs;
struct AppendEntriesArgsDefaultTypeInternal;
extern AppendEntriesArgsDefaultTypeInternal _AppendEntriesArgs_default_instance_;
class AppendEntriesReply;
struct AppendEntriesReplyDefaultTypeInternal;
extern AppendEntriesReplyDefaultTypeInternal _AppendEntriesReply_default_instance_;
class ChangeConfigArgs;
struct ChangeConfigArgsDefaultTypeInternal;
extern ChangeConfigArgsDefaultTypeInternal _ChangeConfigArgs_default_instance_;
class ChangeConfigReply;
struct ChangeConfigReplyDefaultTypeInternal;
extern ChangeConfigReplyDefaultTypeInternal _ChangeConfigReply_default_instance_;
class ConfigChange;
struct ConfigChangeDefaultTypeInternal;
extern ConfigChangeDefaultTypeInternal _ConfigChange_default_instance_;
class InstallSnapshotRequest;
struct InstallSnapshotRequestDefaultTypeInternal;
extern InstallSnapshotRequestDefaultTypeInternal _InstallSnapshotRequest_default_instance_;
class InstallSnapshotResponse;
struct InstallSnapshotResponseDefaultTypeInternal;
extern InstallSnapshotResponseDefaultTypeInternal _InstallSnapshotResponse_default_instance_;
class LogEntry;
struct LogEntryDefaultTypeInternal;
extern LogEntryDefaultTypeInternal _LogEntry_default_instance_;
class RequestVoteArgs;
struct RequestVoteArgsDefaultTypeInternal;
extern RequestVoteArgsDefaultTypeInternal _RequestVoteArgs_default_instance_;
class RequestVoteReply;
struct RequestVoteReplyDefaultTypeInternal;
extern RequestVoteReplyDefaultTypeInternal _RequestVoteReply_default_instance_;
}  // namespace raftRpcProctoc
PROTOBUF_NAMESPACE_OPEN
template<> ::raftRpcProctoc::AppendEntriesArgs* Arena::CreateMaybeMessage<::raftRpcProctoc::AppendEntriesArgs>(Arena*);
template<> ::raftRpcProctoc::AppendEntriesReply* Arena::CreateMaybeMessage<::raftRpcProctoc::AppendEntriesReply>(Arena*);
template<> ::raftRpcProctoc::ChangeConfigArgs* Arena::CreateMaybeMessage<::raftRpcProctoc::ChangeConfigArgs>(Arena*);
template<> ::raftRpcProctoc::ChangeConfigReply* Arena::CreateMaybeMessage<::raftRpcProctoc::ChangeConfigReply>(Arena*);
template<> ::raftRpcProctoc::ConfigChange* Arena::CreateMaybeMessage<::raftRpcProctoc::ConfigChange>(Arena*);
template<> ::raftRpcProctoc::InstallSnapshotRequest* Arena::CreateMaybeMessage<::raftRpcProctoc::InstallSnapshotRequest>(Arena*);
template<> ::raftRpcProctoc::InstallSnapshotResponse* Arena::CreateMaybeMessage<::raftRpcProctoc::InstallSnapshotResponse>(Arena*);
template<> ::raftRpcProctoc::LogEntry* Arena::CreateMaybeMessage<::raftRpcProctoc::LogEntry>(Arena*);
template<> ::raftRpcProctoc::RequestVoteArgs* Arena::CreateMaybeMessage<::raftRpcProctoc::RequestVoteArgs>(Arena*);
template<> ::raftRpcProctoc::RequestVoteReply* Arena::CreateMaybeMessage<::raftRpcProctoc::RequestVoteReply>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace raftRpcProctoc {

enum ConfigChangeType : int {
  ADD_NODE = 0,
  REMOVE_NODE = 1,
  ConfigChangeType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  ConfigChangeType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool ConfigChangeType_IsValid(int value);
constexpr ConfigChangeType ConfigChangeType_MIN = ADD_NODE;
constexpr ConfigChangeType ConfigChangeType_MAX = REMOVE_NODE;
constexpr int ConfigChangeType_ARRAYSIZE = ConfigChangeType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ConfigChangeType_descriptor();
template<typename T>
inline const std::string& ConfigChangeType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ConfigChangeType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ConfigChangeType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ConfigChangeType_descriptor(), enum_t_value);
}
inline bool ConfigChangeType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ConfigChangeType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ConfigChangeType>(
    ConfigChangeType_descriptor(), name, value);
}
// ===================================================================

class ConfigChange final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:raftRpcProctoc.ConfigChange) */ {
 public:
  inline ConfigChange() : ConfigChange(nullptr) {}
  ~ConfigChange() override;
  explicit PROTOBUF_CONSTEXPR ConfigChange(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ConfigChange(const ConfigChange& from);
  ConfigChange(ConfigChange&& from) noexcept
    : ConfigChange() {
    *this = ::std::move(from);
  }

  inline ConfigChange& operator=(const ConfigChange& from) {
    CopyFrom(from);
    return *this;
  }
  inline ConfigChange& operator=(ConfigChange&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ConfigChange& default_instance() {
    return *internal_default_instance();
  }
  static inline const ConfigChange* internal_default_instance() {
    return reinterpret_cast<const ConfigChange*>(
               &_ConfigChange_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ConfigChange& a, ConfigChange& b) {
    a.Swap(&b);
  }
  inline void Swap(ConfigChange* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ConfigChange* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ConfigChange* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ConfigChange>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ConfigChange& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ConfigChange& from) {
    ConfigChange::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ConfigChange* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "raftRpcProctoc.ConfigChange";
  }
  protected:
  explicit ConfigChange(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNodeIdFieldNumber = 2,
    kAddressFieldNumber = 3,
    kTypeFieldNumber = 1,
  };
  // string NodeId = 2;
  void clear_nodeid();
  const std::string& nodeid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_nodeid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_nodeid();
  PROTOBUF_NODISCARD std::string* release_nodeid();
  void set_allocated_nodeid(std::string* nodeid);
  private:
  const std::string& _internal_nodeid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_nodeid(const std::string& value);
  std::string* _internal_mutable_nodeid();
  public:

  // string Address = 3;
  void clear_address();
  const std::string& address() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_address(ArgT0&& arg0, ArgT... args);
  std::string* mutable_address();
  PROTOBUF_NODISCARD std::string* release_address();
  void set_allocated_address(std::string* address);
  private:
  const std::string& _internal_address() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_address(const std::string& value);
  std::string* _internal_mutable_address();
  public:

  // .raftRpcProctoc.ConfigChangeType Type = 1;
  void clear_type();
  ::raftRpcProctoc::ConfigChangeType type() const;
  void set_type(::raftRpcProctoc::ConfigChangeType value);
  private:
  ::raftRpcProctoc::ConfigChangeType _internal_type() const;
  void _internal_set_type(::raftRpcProctoc::ConfigChangeType value);
  public:

  // @@protoc_insertion_point(class_scope:raftRpcProctoc.ConfigChange)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr nodeid_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr address_;
    int type_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_raftRpcPro_2fraftRPC_2eproto;
};
// -------------------------------------------------------------------

class LogEntry final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:raftRpcProctoc.LogEntry) */ {
 public:
  inline LogEntry() : LogEntry(nullptr) {}
  ~LogEntry() override;
  explicit PROTOBUF_CONSTEXPR LogEntry(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LogEntry(const LogEntry& from);
  LogEntry(LogEntry&& from) noexcept
    : LogEntry() {
    *this = ::std::move(from);
  }

  inline LogEntry& operator=(const LogEntry& from) {
    CopyFrom(from);
    return *this;
  }
  inline LogEntry& operator=(LogEntry&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LogEntry& default_instance() {
    return *internal_default_instance();
  }
  static inline const LogEntry* internal_default_instance() {
    return reinterpret_cast<const LogEntry*>(
               &_LogEntry_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(LogEntry& a, LogEntry& b) {
    a.Swap(&b);
  }
  inline void Swap(LogEntry* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LogEntry* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LogEntry* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LogEntry>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LogEntry& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const LogEntry& from) {
    LogEntry::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LogEntry* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "raftRpcProctoc.LogEntry";
  }
  protected:
  explicit LogEntry(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCommandFieldNumber = 1,
    kConfigChangeFieldNumber = 5,
    kLogTermFieldNumber = 2,
    kLogIndexFieldNumber = 3,
    kIsConfigChangeFieldNumber = 4,
  };
  // bytes Command = 1;
  void clear_command();
  const std::string& command() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_command(ArgT0&& arg0, ArgT... args);
  std::string* mutable_command();
  PROTOBUF_NODISCARD std::string* release_command();
  void set_allocated_command(std::string* command);
  private:
  const std::string& _internal_command() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_command(const std::string& value);
  std::string* _internal_mutable_command();
  public:

  // .raftRpcProctoc.ConfigChange ConfigChange = 5;
  bool has_configchange() const;
  private:
  bool _internal_has_configchange() const;
  public:
  void clear_configchange();
  const ::raftRpcProctoc::ConfigChange& configchange() const;
  PROTOBUF_NODISCARD ::raftRpcProctoc::ConfigChange* release_configchange();
  ::raftRpcProctoc::ConfigChange* mutable_configchange();
  void set_allocated_configchange(::raftRpcProctoc::ConfigChange* configchange);
  private:
  const ::raftRpcProctoc::ConfigChange& _internal_configchange() const;
  ::raftRpcProctoc::ConfigChange* _internal_mutable_configchange();
  public:
  void unsafe_arena_set_allocated_configchange(
      ::raftRpcProctoc::ConfigChange* configchange);
  ::raftRpcProctoc::ConfigChange* unsafe_arena_release_configchange();

  // int32 LogTerm = 2;
  void clear_logterm();
  int32_t logterm() const;
  void set_logterm(int32_t value);
  private:
  int32_t _internal_logterm() const;
  void _internal_set_logterm(int32_t value);
  public:

  // int32 LogIndex = 3;
  void clear_logindex();
  int32_t logindex() const;
  void set_logindex(int32_t value);
  private:
  int32_t _internal_logindex() const;
  void _internal_set_logindex(int32_t value);
  public:

  // bool IsConfigChange = 4;
  void clear_isconfigchange();
  bool isconfigchange() const;
  void set_isconfigchange(bool value);
  private:
  bool _internal_isconfigchange() const;
  void _internal_set_isconfigchange(bool value);
  public:

  // @@protoc_insertion_point(class_scope:raftRpcProctoc.LogEntry)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr command_;
    ::raftRpcProctoc::ConfigChange* configchange_;
    int32_t logterm_;
    int32_t logindex_;
    bool isconfigchange_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_raftRpcPro_2fraftRPC_2eproto;
};
// -------------------------------------------------------------------

class AppendEntriesArgs final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:raftRpcProctoc.AppendEntriesArgs) */ {
 public:
  inline AppendEntriesArgs() : AppendEntriesArgs(nullptr) {}
  ~AppendEntriesArgs() override;
  explicit PROTOBUF_CONSTEXPR AppendEntriesArgs(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AppendEntriesArgs(const AppendEntriesArgs& from);
  AppendEntriesArgs(AppendEntriesArgs&& from) noexcept
    : AppendEntriesArgs() {
    *this = ::std::move(from);
  }

  inline AppendEntriesArgs& operator=(const AppendEntriesArgs& from) {
    CopyFrom(from);
    return *this;
  }
  inline AppendEntriesArgs& operator=(AppendEntriesArgs&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AppendEntriesArgs& default_instance() {
    return *internal_default_instance();
  }
  static inline const AppendEntriesArgs* internal_default_instance() {
    return reinterpret_cast<const AppendEntriesArgs*>(
               &_AppendEntriesArgs_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(AppendEntriesArgs& a, AppendEntriesArgs& b) {
    a.Swap(&b);
  }
  inline void Swap(AppendEntriesArgs* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AppendEntriesArgs* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AppendEntriesArgs* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AppendEntriesArgs>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AppendEntriesArgs& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const AppendEntriesArgs& from) {
    AppendEntriesArgs::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AppendEntriesArgs* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "raftRpcProctoc.AppendEntriesArgs";
  }
  protected:
  explicit AppendEntriesArgs(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEntriesFieldNumber = 5,
    kTermFieldNumber = 1,
    kLeaderIdFieldNumber = 2,
    kPrevLogIndexFieldNumber = 3,
    kPrevLogTermFieldNumber = 4,
    kLeaderCommitFieldNumber = 6,
  };
  // repeated .raftRpcProctoc.LogEntry Entries = 5;
  int entries_size() const;
  private:
  int _internal_entries_size() const;
  public:
  void clear_entries();
  ::raftRpcProctoc::LogEntry* mutable_entries(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::raftRpcProctoc::LogEntry >*
      mutable_entries();
  private:
  const ::raftRpcProctoc::LogEntry& _internal_entries(int index) const;
  ::raftRpcProctoc::LogEntry* _internal_add_entries();
  public:
  const ::raftRpcProctoc::LogEntry& entries(int index) const;
  ::raftRpcProctoc::LogEntry* add_entries();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::raftRpcProctoc::LogEntry >&
      entries() const;

  // int32 Term = 1;
  void clear_term();
  int32_t term() const;
  void set_term(int32_t value);
  private:
  int32_t _internal_term() const;
  void _internal_set_term(int32_t value);
  public:

  // int32 LeaderId = 2;
  void clear_leaderid();
  int32_t leaderid() const;
  void set_leaderid(int32_t value);
  private:
  int32_t _internal_leaderid() const;
  void _internal_set_leaderid(int32_t value);
  public:

  // int32 PrevLogIndex = 3;
  void clear_prevlogindex();
  int32_t prevlogindex() const;
  void set_prevlogindex(int32_t value);
  private:
  int32_t _internal_prevlogindex() const;
  void _internal_set_prevlogindex(int32_t value);
  public:

  // int32 PrevLogTerm = 4;
  void clear_prevlogterm();
  int32_t prevlogterm() const;
  void set_prevlogterm(int32_t value);
  private:
  int32_t _internal_prevlogterm() const;
  void _internal_set_prevlogterm(int32_t value);
  public:

  // int32 LeaderCommit = 6;
  void clear_leadercommit();
  int32_t leadercommit() const;
  void set_leadercommit(int32_t value);
  private:
  int32_t _internal_leadercommit() const;
  void _internal_set_leadercommit(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:raftRpcProctoc.AppendEntriesArgs)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::raftRpcProctoc::LogEntry > entries_;
    int32_t term_;
    int32_t leaderid_;
    int32_t prevlogindex_;
    int32_t prevlogterm_;
    int32_t leadercommit_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_raftRpcPro_2fraftRPC_2eproto;
};
// -------------------------------------------------------------------

class AppendEntriesReply final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:raftRpcProctoc.AppendEntriesReply) */ {
 public:
  inline AppendEntriesReply() : AppendEntriesReply(nullptr) {}
  ~AppendEntriesReply() override;
  explicit PROTOBUF_CONSTEXPR AppendEntriesReply(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AppendEntriesReply(const AppendEntriesReply& from);
  AppendEntriesReply(AppendEntriesReply&& from) noexcept
    : AppendEntriesReply() {
    *this = ::std::move(from);
  }

  inline AppendEntriesReply& operator=(const AppendEntriesReply& from) {
    CopyFrom(from);
    return *this;
  }
  inline AppendEntriesReply& operator=(AppendEntriesReply&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AppendEntriesReply& default_instance() {
    return *internal_default_instance();
  }
  static inline const AppendEntriesReply* internal_default_instance() {
    return reinterpret_cast<const AppendEntriesReply*>(
               &_AppendEntriesReply_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(AppendEntriesReply& a, AppendEntriesReply& b) {
    a.Swap(&b);
  }
  inline void Swap(AppendEntriesReply* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AppendEntriesReply* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AppendEntriesReply* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AppendEntriesReply>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AppendEntriesReply& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const AppendEntriesReply& from) {
    AppendEntriesReply::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AppendEntriesReply* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "raftRpcProctoc.AppendEntriesReply";
  }
  protected:
  explicit AppendEntriesReply(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTermFieldNumber = 1,
    kSuccessFieldNumber = 2,
    kUpdateNextIndexFieldNumber = 3,
    kAppStateFieldNumber = 4,
  };
  // int32 Term = 1;
  void clear_term();
  int32_t term() const;
  void set_term(int32_t value);
  private:
  int32_t _internal_term() const;
  void _internal_set_term(int32_t value);
  public:

  // bool Success = 2;
  void clear_success();
  bool success() const;
  void set_success(bool value);
  private:
  bool _internal_success() const;
  void _internal_set_success(bool value);
  public:

  // int32 UpdateNextIndex = 3;
  void clear_updatenextindex();
  int32_t updatenextindex() const;
  void set_updatenextindex(int32_t value);
  private:
  int32_t _internal_updatenextindex() const;
  void _internal_set_updatenextindex(int32_t value);
  public:

  // int32 AppState = 4;
  void clear_appstate();
  int32_t appstate() const;
  void set_appstate(int32_t value);
  private:
  int32_t _internal_appstate() const;
  void _internal_set_appstate(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:raftRpcProctoc.AppendEntriesReply)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int32_t term_;
    bool success_;
    int32_t updatenextindex_;
    int32_t appstate_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_raftRpcPro_2fraftRPC_2eproto;
};
// -------------------------------------------------------------------

class RequestVoteArgs final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:raftRpcProctoc.RequestVoteArgs) */ {
 public:
  inline RequestVoteArgs() : RequestVoteArgs(nullptr) {}
  ~RequestVoteArgs() override;
  explicit PROTOBUF_CONSTEXPR RequestVoteArgs(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RequestVoteArgs(const RequestVoteArgs& from);
  RequestVoteArgs(RequestVoteArgs&& from) noexcept
    : RequestVoteArgs() {
    *this = ::std::move(from);
  }

  inline RequestVoteArgs& operator=(const RequestVoteArgs& from) {
    CopyFrom(from);
    return *this;
  }
  inline RequestVoteArgs& operator=(RequestVoteArgs&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RequestVoteArgs& default_instance() {
    return *internal_default_instance();
  }
  static inline const RequestVoteArgs* internal_default_instance() {
    return reinterpret_cast<const RequestVoteArgs*>(
               &_RequestVoteArgs_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(RequestVoteArgs& a, RequestVoteArgs& b) {
    a.Swap(&b);
  }
  inline void Swap(RequestVoteArgs* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RequestVoteArgs* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RequestVoteArgs* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RequestVoteArgs>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RequestVoteArgs& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RequestVoteArgs& from) {
    RequestVoteArgs::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RequestVoteArgs* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "raftRpcProctoc.RequestVoteArgs";
  }
  protected:
  explicit RequestVoteArgs(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTermFieldNumber = 1,
    kCandidateIdFieldNumber = 2,
    kLastLogIndexFieldNumber = 3,
    kLastLogTermFieldNumber = 4,
  };
  // int32 Term = 1;
  void clear_term();
  int32_t term() const;
  void set_term(int32_t value);
  private:
  int32_t _internal_term() const;
  void _internal_set_term(int32_t value);
  public:

  // int32 CandidateId = 2;
  void clear_candidateid();
  int32_t candidateid() const;
  void set_candidateid(int32_t value);
  private:
  int32_t _internal_candidateid() const;
  void _internal_set_candidateid(int32_t value);
  public:

  // int32 LastLogIndex = 3;
  void clear_lastlogindex();
  int32_t lastlogindex() const;
  void set_lastlogindex(int32_t value);
  private:
  int32_t _internal_lastlogindex() const;
  void _internal_set_lastlogindex(int32_t value);
  public:

  // int32 LastLogTerm = 4;
  void clear_lastlogterm();
  int32_t lastlogterm() const;
  void set_lastlogterm(int32_t value);
  private:
  int32_t _internal_lastlogterm() const;
  void _internal_set_lastlogterm(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:raftRpcProctoc.RequestVoteArgs)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int32_t term_;
    int32_t candidateid_;
    int32_t lastlogindex_;
    int32_t lastlogterm_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_raftRpcPro_2fraftRPC_2eproto;
};
// -------------------------------------------------------------------

class RequestVoteReply final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:raftRpcProctoc.RequestVoteReply) */ {
 public:
  inline RequestVoteReply() : RequestVoteReply(nullptr) {}
  ~RequestVoteReply() override;
  explicit PROTOBUF_CONSTEXPR RequestVoteReply(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RequestVoteReply(const RequestVoteReply& from);
  RequestVoteReply(RequestVoteReply&& from) noexcept
    : RequestVoteReply() {
    *this = ::std::move(from);
  }

  inline RequestVoteReply& operator=(const RequestVoteReply& from) {
    CopyFrom(from);
    return *this;
  }
  inline RequestVoteReply& operator=(RequestVoteReply&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RequestVoteReply& default_instance() {
    return *internal_default_instance();
  }
  static inline const RequestVoteReply* internal_default_instance() {
    return reinterpret_cast<const RequestVoteReply*>(
               &_RequestVoteReply_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(RequestVoteReply& a, RequestVoteReply& b) {
    a.Swap(&b);
  }
  inline void Swap(RequestVoteReply* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RequestVoteReply* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RequestVoteReply* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RequestVoteReply>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RequestVoteReply& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RequestVoteReply& from) {
    RequestVoteReply::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RequestVoteReply* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "raftRpcProctoc.RequestVoteReply";
  }
  protected:
  explicit RequestVoteReply(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTermFieldNumber = 1,
    kVoteGrantedFieldNumber = 2,
    kVoteStateFieldNumber = 3,
  };
  // int32 Term = 1;
  void clear_term();
  int32_t term() const;
  void set_term(int32_t value);
  private:
  int32_t _internal_term() const;
  void _internal_set_term(int32_t value);
  public:

  // bool VoteGranted = 2;
  void clear_votegranted();
  bool votegranted() const;
  void set_votegranted(bool value);
  private:
  bool _internal_votegranted() const;
  void _internal_set_votegranted(bool value);
  public:

  // int32 VoteState = 3;
  void clear_votestate();
  int32_t votestate() const;
  void set_votestate(int32_t value);
  private:
  int32_t _internal_votestate() const;
  void _internal_set_votestate(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:raftRpcProctoc.RequestVoteReply)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int32_t term_;
    bool votegranted_;
    int32_t votestate_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_raftRpcPro_2fraftRPC_2eproto;
};
// -------------------------------------------------------------------

class InstallSnapshotRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:raftRpcProctoc.InstallSnapshotRequest) */ {
 public:
  inline InstallSnapshotRequest() : InstallSnapshotRequest(nullptr) {}
  ~InstallSnapshotRequest() override;
  explicit PROTOBUF_CONSTEXPR InstallSnapshotRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  InstallSnapshotRequest(const InstallSnapshotRequest& from);
  InstallSnapshotRequest(InstallSnapshotRequest&& from) noexcept
    : InstallSnapshotRequest() {
    *this = ::std::move(from);
  }

  inline InstallSnapshotRequest& operator=(const InstallSnapshotRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline InstallSnapshotRequest& operator=(InstallSnapshotRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const InstallSnapshotRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const InstallSnapshotRequest* internal_default_instance() {
    return reinterpret_cast<const InstallSnapshotRequest*>(
               &_InstallSnapshotRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(InstallSnapshotRequest& a, InstallSnapshotRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(InstallSnapshotRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(InstallSnapshotRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  InstallSnapshotRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<InstallSnapshotRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const InstallSnapshotRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const InstallSnapshotRequest& from) {
    InstallSnapshotRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(InstallSnapshotRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "raftRpcProctoc.InstallSnapshotRequest";
  }
  protected:
  explicit InstallSnapshotRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDataFieldNumber = 5,
    kLeaderIdFieldNumber = 1,
    kTermFieldNumber = 2,
    kLastSnapShotIncludeIndexFieldNumber = 3,
    kLastSnapShotIncludeTermFieldNumber = 4,
  };
  // bytes Data = 5;
  void clear_data();
  const std::string& data() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_data(ArgT0&& arg0, ArgT... args);
  std::string* mutable_data();
  PROTOBUF_NODISCARD std::string* release_data();
  void set_allocated_data(std::string* data);
  private:
  const std::string& _internal_data() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_data(const std::string& value);
  std::string* _internal_mutable_data();
  public:

  // int32 LeaderId = 1;
  void clear_leaderid();
  int32_t leaderid() const;
  void set_leaderid(int32_t value);
  private:
  int32_t _internal_leaderid() const;
  void _internal_set_leaderid(int32_t value);
  public:

  // int32 Term = 2;
  void clear_term();
  int32_t term() const;
  void set_term(int32_t value);
  private:
  int32_t _internal_term() const;
  void _internal_set_term(int32_t value);
  public:

  // int32 LastSnapShotIncludeIndex = 3;
  void clear_lastsnapshotincludeindex();
  int32_t lastsnapshotincludeindex() const;
  void set_lastsnapshotincludeindex(int32_t value);
  private:
  int32_t _internal_lastsnapshotincludeindex() const;
  void _internal_set_lastsnapshotincludeindex(int32_t value);
  public:

  // int32 LastSnapShotIncludeTerm = 4;
  void clear_lastsnapshotincludeterm();
  int32_t lastsnapshotincludeterm() const;
  void set_lastsnapshotincludeterm(int32_t value);
  private:
  int32_t _internal_lastsnapshotincludeterm() const;
  void _internal_set_lastsnapshotincludeterm(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:raftRpcProctoc.InstallSnapshotRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr data_;
    int32_t leaderid_;
    int32_t term_;
    int32_t lastsnapshotincludeindex_;
    int32_t lastsnapshotincludeterm_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_raftRpcPro_2fraftRPC_2eproto;
};
// -------------------------------------------------------------------

class InstallSnapshotResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:raftRpcProctoc.InstallSnapshotResponse) */ {
 public:
  inline InstallSnapshotResponse() : InstallSnapshotResponse(nullptr) {}
  ~InstallSnapshotResponse() override;
  explicit PROTOBUF_CONSTEXPR InstallSnapshotResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  InstallSnapshotResponse(const InstallSnapshotResponse& from);
  InstallSnapshotResponse(InstallSnapshotResponse&& from) noexcept
    : InstallSnapshotResponse() {
    *this = ::std::move(from);
  }

  inline InstallSnapshotResponse& operator=(const InstallSnapshotResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline InstallSnapshotResponse& operator=(InstallSnapshotResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const InstallSnapshotResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const InstallSnapshotResponse* internal_default_instance() {
    return reinterpret_cast<const InstallSnapshotResponse*>(
               &_InstallSnapshotResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(InstallSnapshotResponse& a, InstallSnapshotResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(InstallSnapshotResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(InstallSnapshotResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  InstallSnapshotResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<InstallSnapshotResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const InstallSnapshotResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const InstallSnapshotResponse& from) {
    InstallSnapshotResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(InstallSnapshotResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "raftRpcProctoc.InstallSnapshotResponse";
  }
  protected:
  explicit InstallSnapshotResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTermFieldNumber = 1,
  };
  // int32 Term = 1;
  void clear_term();
  int32_t term() const;
  void set_term(int32_t value);
  private:
  int32_t _internal_term() const;
  void _internal_set_term(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:raftRpcProctoc.InstallSnapshotResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int32_t term_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_raftRpcPro_2fraftRPC_2eproto;
};
// -------------------------------------------------------------------

class ChangeConfigArgs final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:raftRpcProctoc.ChangeConfigArgs) */ {
 public:
  inline ChangeConfigArgs() : ChangeConfigArgs(nullptr) {}
  ~ChangeConfigArgs() override;
  explicit PROTOBUF_CONSTEXPR ChangeConfigArgs(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ChangeConfigArgs(const ChangeConfigArgs& from);
  ChangeConfigArgs(ChangeConfigArgs&& from) noexcept
    : ChangeConfigArgs() {
    *this = ::std::move(from);
  }

  inline ChangeConfigArgs& operator=(const ChangeConfigArgs& from) {
    CopyFrom(from);
    return *this;
  }
  inline ChangeConfigArgs& operator=(ChangeConfigArgs&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ChangeConfigArgs& default_instance() {
    return *internal_default_instance();
  }
  static inline const ChangeConfigArgs* internal_default_instance() {
    return reinterpret_cast<const ChangeConfigArgs*>(
               &_ChangeConfigArgs_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(ChangeConfigArgs& a, ChangeConfigArgs& b) {
    a.Swap(&b);
  }
  inline void Swap(ChangeConfigArgs* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ChangeConfigArgs* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ChangeConfigArgs* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ChangeConfigArgs>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ChangeConfigArgs& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ChangeConfigArgs& from) {
    ChangeConfigArgs::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ChangeConfigArgs* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "raftRpcProctoc.ChangeConfigArgs";
  }
  protected:
  explicit ChangeConfigArgs(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNodeIdFieldNumber = 2,
    kAddressFieldNumber = 3,
    kTypeFieldNumber = 1,
  };
  // string NodeId = 2;
  void clear_nodeid();
  const std::string& nodeid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_nodeid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_nodeid();
  PROTOBUF_NODISCARD std::string* release_nodeid();
  void set_allocated_nodeid(std::string* nodeid);
  private:
  const std::string& _internal_nodeid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_nodeid(const std::string& value);
  std::string* _internal_mutable_nodeid();
  public:

  // string Address = 3;
  void clear_address();
  const std::string& address() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_address(ArgT0&& arg0, ArgT... args);
  std::string* mutable_address();
  PROTOBUF_NODISCARD std::string* release_address();
  void set_allocated_address(std::string* address);
  private:
  const std::string& _internal_address() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_address(const std::string& value);
  std::string* _internal_mutable_address();
  public:

  // .raftRpcProctoc.ConfigChangeType Type = 1;
  void clear_type();
  ::raftRpcProctoc::ConfigChangeType type() const;
  void set_type(::raftRpcProctoc::ConfigChangeType value);
  private:
  ::raftRpcProctoc::ConfigChangeType _internal_type() const;
  void _internal_set_type(::raftRpcProctoc::ConfigChangeType value);
  public:

  // @@protoc_insertion_point(class_scope:raftRpcProctoc.ChangeConfigArgs)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr nodeid_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr address_;
    int type_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_raftRpcPro_2fraftRPC_2eproto;
};
// -------------------------------------------------------------------

class ChangeConfigReply final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:raftRpcProctoc.ChangeConfigReply) */ {
 public:
  inline ChangeConfigReply() : ChangeConfigReply(nullptr) {}
  ~ChangeConfigReply() override;
  explicit PROTOBUF_CONSTEXPR ChangeConfigReply(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ChangeConfigReply(const ChangeConfigReply& from);
  ChangeConfigReply(ChangeConfigReply&& from) noexcept
    : ChangeConfigReply() {
    *this = ::std::move(from);
  }

  inline ChangeConfigReply& operator=(const ChangeConfigReply& from) {
    CopyFrom(from);
    return *this;
  }
  inline ChangeConfigReply& operator=(ChangeConfigReply&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ChangeConfigReply& default_instance() {
    return *internal_default_instance();
  }
  static inline const ChangeConfigReply* internal_default_instance() {
    return reinterpret_cast<const ChangeConfigReply*>(
               &_ChangeConfigReply_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(ChangeConfigReply& a, ChangeConfigReply& b) {
    a.Swap(&b);
  }
  inline void Swap(ChangeConfigReply* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ChangeConfigReply* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ChangeConfigReply* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ChangeConfigReply>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ChangeConfigReply& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ChangeConfigReply& from) {
    ChangeConfigReply::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ChangeConfigReply* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "raftRpcProctoc.ChangeConfigReply";
  }
  protected:
  explicit ChangeConfigReply(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kErrorFieldNumber = 2,
    kSuccessFieldNumber = 1,
    kIsLeaderFieldNumber = 3,
  };
  // string Error = 2;
  void clear_error();
  const std::string& error() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_error(ArgT0&& arg0, ArgT... args);
  std::string* mutable_error();
  PROTOBUF_NODISCARD std::string* release_error();
  void set_allocated_error(std::string* error);
  private:
  const std::string& _internal_error() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_error(const std::string& value);
  std::string* _internal_mutable_error();
  public:

  // bool Success = 1;
  void clear_success();
  bool success() const;
  void set_success(bool value);
  private:
  bool _internal_success() const;
  void _internal_set_success(bool value);
  public:

  // bool IsLeader = 3;
  void clear_isleader();
  bool isleader() const;
  void set_isleader(bool value);
  private:
  bool _internal_isleader() const;
  void _internal_set_isleader(bool value);
  public:

  // @@protoc_insertion_point(class_scope:raftRpcProctoc.ChangeConfigReply)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr error_;
    bool success_;
    bool isleader_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_raftRpcPro_2fraftRPC_2eproto;
};
// ===================================================================

class raftRpc_Stub;

class raftRpc : public ::PROTOBUF_NAMESPACE_ID::Service {
 protected:
  // This class should be treated as an abstract interface.
  inline raftRpc() {};
 public:
  virtual ~raftRpc();

  typedef raftRpc_Stub Stub;

  static const ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor* descriptor();

  virtual void AppendEntries(::PROTOBUF_NAMESPACE_ID::RpcController* controller,
                       const ::raftRpcProctoc::AppendEntriesArgs* request,
                       ::raftRpcProctoc::AppendEntriesReply* response,
                       ::google::protobuf::Closure* done);
  virtual void InstallSnapshot(::PROTOBUF_NAMESPACE_ID::RpcController* controller,
                       const ::raftRpcProctoc::InstallSnapshotRequest* request,
                       ::raftRpcProctoc::InstallSnapshotResponse* response,
                       ::google::protobuf::Closure* done);
  virtual void RequestVote(::PROTOBUF_NAMESPACE_ID::RpcController* controller,
                       const ::raftRpcProctoc::RequestVoteArgs* request,
                       ::raftRpcProctoc::RequestVoteReply* response,
                       ::google::protobuf::Closure* done);
  virtual void ChangeConfig(::PROTOBUF_NAMESPACE_ID::RpcController* controller,
                       const ::raftRpcProctoc::ChangeConfigArgs* request,
                       ::raftRpcProctoc::ChangeConfigReply* response,
                       ::google::protobuf::Closure* done);

  // implements Service ----------------------------------------------

  const ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor* GetDescriptor();
  void CallMethod(const ::PROTOBUF_NAMESPACE_ID::MethodDescriptor* method,
                  ::PROTOBUF_NAMESPACE_ID::RpcController* controller,
                  const ::PROTOBUF_NAMESPACE_ID::Message* request,
                  ::PROTOBUF_NAMESPACE_ID::Message* response,
                  ::google::protobuf::Closure* done);
  const ::PROTOBUF_NAMESPACE_ID::Message& GetRequestPrototype(
    const ::PROTOBUF_NAMESPACE_ID::MethodDescriptor* method) const;
  const ::PROTOBUF_NAMESPACE_ID::Message& GetResponsePrototype(
    const ::PROTOBUF_NAMESPACE_ID::MethodDescriptor* method) const;

 private:
  GOOGLE_DISALLOW_EVIL_CONSTRUCTORS(raftRpc);
};

class raftRpc_Stub : public raftRpc {
 public:
  raftRpc_Stub(::PROTOBUF_NAMESPACE_ID::RpcChannel* channel);
  raftRpc_Stub(::PROTOBUF_NAMESPACE_ID::RpcChannel* channel,
                   ::PROTOBUF_NAMESPACE_ID::Service::ChannelOwnership ownership);
  ~raftRpc_Stub();

  inline ::PROTOBUF_NAMESPACE_ID::RpcChannel* channel() { return channel_; }

  // implements raftRpc ------------------------------------------

  void AppendEntries(::PROTOBUF_NAMESPACE_ID::RpcController* controller,
                       const ::raftRpcProctoc::AppendEntriesArgs* request,
                       ::raftRpcProctoc::AppendEntriesReply* response,
                       ::google::protobuf::Closure* done);
  void InstallSnapshot(::PROTOBUF_NAMESPACE_ID::RpcController* controller,
                       const ::raftRpcProctoc::InstallSnapshotRequest* request,
                       ::raftRpcProctoc::InstallSnapshotResponse* response,
                       ::google::protobuf::Closure* done);
  void RequestVote(::PROTOBUF_NAMESPACE_ID::RpcController* controller,
                       const ::raftRpcProctoc::RequestVoteArgs* request,
                       ::raftRpcProctoc::RequestVoteReply* response,
                       ::google::protobuf::Closure* done);
  void ChangeConfig(::PROTOBUF_NAMESPACE_ID::RpcController* controller,
                       const ::raftRpcProctoc::ChangeConfigArgs* request,
                       ::raftRpcProctoc::ChangeConfigReply* response,
                       ::google::protobuf::Closure* done);
 private:
  ::PROTOBUF_NAMESPACE_ID::RpcChannel* channel_;
  bool owns_channel_;
  GOOGLE_DISALLOW_EVIL_CONSTRUCTORS(raftRpc_Stub);
};


// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ConfigChange

// .raftRpcProctoc.ConfigChangeType Type = 1;
inline void ConfigChange::clear_type() {
  _impl_.type_ = 0;
}
inline ::raftRpcProctoc::ConfigChangeType ConfigChange::_internal_type() const {
  return static_cast< ::raftRpcProctoc::ConfigChangeType >(_impl_.type_);
}
inline ::raftRpcProctoc::ConfigChangeType ConfigChange::type() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.ConfigChange.Type)
  return _internal_type();
}
inline void ConfigChange::_internal_set_type(::raftRpcProctoc::ConfigChangeType value) {
  
  _impl_.type_ = value;
}
inline void ConfigChange::set_type(::raftRpcProctoc::ConfigChangeType value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:raftRpcProctoc.ConfigChange.Type)
}

// string NodeId = 2;
inline void ConfigChange::clear_nodeid() {
  _impl_.nodeid_.ClearToEmpty();
}
inline const std::string& ConfigChange::nodeid() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.ConfigChange.NodeId)
  return _internal_nodeid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ConfigChange::set_nodeid(ArgT0&& arg0, ArgT... args) {
 
 _impl_.nodeid_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:raftRpcProctoc.ConfigChange.NodeId)
}
inline std::string* ConfigChange::mutable_nodeid() {
  std::string* _s = _internal_mutable_nodeid();
  // @@protoc_insertion_point(field_mutable:raftRpcProctoc.ConfigChange.NodeId)
  return _s;
}
inline const std::string& ConfigChange::_internal_nodeid() const {
  return _impl_.nodeid_.Get();
}
inline void ConfigChange::_internal_set_nodeid(const std::string& value) {
  
  _impl_.nodeid_.Set(value, GetArenaForAllocation());
}
inline std::string* ConfigChange::_internal_mutable_nodeid() {
  
  return _impl_.nodeid_.Mutable(GetArenaForAllocation());
}
inline std::string* ConfigChange::release_nodeid() {
  // @@protoc_insertion_point(field_release:raftRpcProctoc.ConfigChange.NodeId)
  return _impl_.nodeid_.Release();
}
inline void ConfigChange::set_allocated_nodeid(std::string* nodeid) {
  if (nodeid != nullptr) {
    
  } else {
    
  }
  _impl_.nodeid_.SetAllocated(nodeid, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.nodeid_.IsDefault()) {
    _impl_.nodeid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:raftRpcProctoc.ConfigChange.NodeId)
}

// string Address = 3;
inline void ConfigChange::clear_address() {
  _impl_.address_.ClearToEmpty();
}
inline const std::string& ConfigChange::address() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.ConfigChange.Address)
  return _internal_address();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ConfigChange::set_address(ArgT0&& arg0, ArgT... args) {
 
 _impl_.address_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:raftRpcProctoc.ConfigChange.Address)
}
inline std::string* ConfigChange::mutable_address() {
  std::string* _s = _internal_mutable_address();
  // @@protoc_insertion_point(field_mutable:raftRpcProctoc.ConfigChange.Address)
  return _s;
}
inline const std::string& ConfigChange::_internal_address() const {
  return _impl_.address_.Get();
}
inline void ConfigChange::_internal_set_address(const std::string& value) {
  
  _impl_.address_.Set(value, GetArenaForAllocation());
}
inline std::string* ConfigChange::_internal_mutable_address() {
  
  return _impl_.address_.Mutable(GetArenaForAllocation());
}
inline std::string* ConfigChange::release_address() {
  // @@protoc_insertion_point(field_release:raftRpcProctoc.ConfigChange.Address)
  return _impl_.address_.Release();
}
inline void ConfigChange::set_allocated_address(std::string* address) {
  if (address != nullptr) {
    
  } else {
    
  }
  _impl_.address_.SetAllocated(address, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.address_.IsDefault()) {
    _impl_.address_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:raftRpcProctoc.ConfigChange.Address)
}

// -------------------------------------------------------------------

// LogEntry

// bytes Command = 1;
inline void LogEntry::clear_command() {
  _impl_.command_.ClearToEmpty();
}
inline const std::string& LogEntry::command() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.LogEntry.Command)
  return _internal_command();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void LogEntry::set_command(ArgT0&& arg0, ArgT... args) {
 
 _impl_.command_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:raftRpcProctoc.LogEntry.Command)
}
inline std::string* LogEntry::mutable_command() {
  std::string* _s = _internal_mutable_command();
  // @@protoc_insertion_point(field_mutable:raftRpcProctoc.LogEntry.Command)
  return _s;
}
inline const std::string& LogEntry::_internal_command() const {
  return _impl_.command_.Get();
}
inline void LogEntry::_internal_set_command(const std::string& value) {
  
  _impl_.command_.Set(value, GetArenaForAllocation());
}
inline std::string* LogEntry::_internal_mutable_command() {
  
  return _impl_.command_.Mutable(GetArenaForAllocation());
}
inline std::string* LogEntry::release_command() {
  // @@protoc_insertion_point(field_release:raftRpcProctoc.LogEntry.Command)
  return _impl_.command_.Release();
}
inline void LogEntry::set_allocated_command(std::string* command) {
  if (command != nullptr) {
    
  } else {
    
  }
  _impl_.command_.SetAllocated(command, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.command_.IsDefault()) {
    _impl_.command_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:raftRpcProctoc.LogEntry.Command)
}

// int32 LogTerm = 2;
inline void LogEntry::clear_logterm() {
  _impl_.logterm_ = 0;
}
inline int32_t LogEntry::_internal_logterm() const {
  return _impl_.logterm_;
}
inline int32_t LogEntry::logterm() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.LogEntry.LogTerm)
  return _internal_logterm();
}
inline void LogEntry::_internal_set_logterm(int32_t value) {
  
  _impl_.logterm_ = value;
}
inline void LogEntry::set_logterm(int32_t value) {
  _internal_set_logterm(value);
  // @@protoc_insertion_point(field_set:raftRpcProctoc.LogEntry.LogTerm)
}

// int32 LogIndex = 3;
inline void LogEntry::clear_logindex() {
  _impl_.logindex_ = 0;
}
inline int32_t LogEntry::_internal_logindex() const {
  return _impl_.logindex_;
}
inline int32_t LogEntry::logindex() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.LogEntry.LogIndex)
  return _internal_logindex();
}
inline void LogEntry::_internal_set_logindex(int32_t value) {
  
  _impl_.logindex_ = value;
}
inline void LogEntry::set_logindex(int32_t value) {
  _internal_set_logindex(value);
  // @@protoc_insertion_point(field_set:raftRpcProctoc.LogEntry.LogIndex)
}

// bool IsConfigChange = 4;
inline void LogEntry::clear_isconfigchange() {
  _impl_.isconfigchange_ = false;
}
inline bool LogEntry::_internal_isconfigchange() const {
  return _impl_.isconfigchange_;
}
inline bool LogEntry::isconfigchange() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.LogEntry.IsConfigChange)
  return _internal_isconfigchange();
}
inline void LogEntry::_internal_set_isconfigchange(bool value) {
  
  _impl_.isconfigchange_ = value;
}
inline void LogEntry::set_isconfigchange(bool value) {
  _internal_set_isconfigchange(value);
  // @@protoc_insertion_point(field_set:raftRpcProctoc.LogEntry.IsConfigChange)
}

// .raftRpcProctoc.ConfigChange ConfigChange = 5;
inline bool LogEntry::_internal_has_configchange() const {
  return this != internal_default_instance() && _impl_.configchange_ != nullptr;
}
inline bool LogEntry::has_configchange() const {
  return _internal_has_configchange();
}
inline void LogEntry::clear_configchange() {
  if (GetArenaForAllocation() == nullptr && _impl_.configchange_ != nullptr) {
    delete _impl_.configchange_;
  }
  _impl_.configchange_ = nullptr;
}
inline const ::raftRpcProctoc::ConfigChange& LogEntry::_internal_configchange() const {
  const ::raftRpcProctoc::ConfigChange* p = _impl_.configchange_;
  return p != nullptr ? *p : reinterpret_cast<const ::raftRpcProctoc::ConfigChange&>(
      ::raftRpcProctoc::_ConfigChange_default_instance_);
}
inline const ::raftRpcProctoc::ConfigChange& LogEntry::configchange() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.LogEntry.ConfigChange)
  return _internal_configchange();
}
inline void LogEntry::unsafe_arena_set_allocated_configchange(
    ::raftRpcProctoc::ConfigChange* configchange) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.configchange_);
  }
  _impl_.configchange_ = configchange;
  if (configchange) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:raftRpcProctoc.LogEntry.ConfigChange)
}
inline ::raftRpcProctoc::ConfigChange* LogEntry::release_configchange() {
  
  ::raftRpcProctoc::ConfigChange* temp = _impl_.configchange_;
  _impl_.configchange_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::raftRpcProctoc::ConfigChange* LogEntry::unsafe_arena_release_configchange() {
  // @@protoc_insertion_point(field_release:raftRpcProctoc.LogEntry.ConfigChange)
  
  ::raftRpcProctoc::ConfigChange* temp = _impl_.configchange_;
  _impl_.configchange_ = nullptr;
  return temp;
}
inline ::raftRpcProctoc::ConfigChange* LogEntry::_internal_mutable_configchange() {
  
  if (_impl_.configchange_ == nullptr) {
    auto* p = CreateMaybeMessage<::raftRpcProctoc::ConfigChange>(GetArenaForAllocation());
    _impl_.configchange_ = p;
  }
  return _impl_.configchange_;
}
inline ::raftRpcProctoc::ConfigChange* LogEntry::mutable_configchange() {
  ::raftRpcProctoc::ConfigChange* _msg = _internal_mutable_configchange();
  // @@protoc_insertion_point(field_mutable:raftRpcProctoc.LogEntry.ConfigChange)
  return _msg;
}
inline void LogEntry::set_allocated_configchange(::raftRpcProctoc::ConfigChange* configchange) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.configchange_;
  }
  if (configchange) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(configchange);
    if (message_arena != submessage_arena) {
      configchange = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, configchange, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.configchange_ = configchange;
  // @@protoc_insertion_point(field_set_allocated:raftRpcProctoc.LogEntry.ConfigChange)
}

// -------------------------------------------------------------------

// AppendEntriesArgs

// int32 Term = 1;
inline void AppendEntriesArgs::clear_term() {
  _impl_.term_ = 0;
}
inline int32_t AppendEntriesArgs::_internal_term() const {
  return _impl_.term_;
}
inline int32_t AppendEntriesArgs::term() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.AppendEntriesArgs.Term)
  return _internal_term();
}
inline void AppendEntriesArgs::_internal_set_term(int32_t value) {
  
  _impl_.term_ = value;
}
inline void AppendEntriesArgs::set_term(int32_t value) {
  _internal_set_term(value);
  // @@protoc_insertion_point(field_set:raftRpcProctoc.AppendEntriesArgs.Term)
}

// int32 LeaderId = 2;
inline void AppendEntriesArgs::clear_leaderid() {
  _impl_.leaderid_ = 0;
}
inline int32_t AppendEntriesArgs::_internal_leaderid() const {
  return _impl_.leaderid_;
}
inline int32_t AppendEntriesArgs::leaderid() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.AppendEntriesArgs.LeaderId)
  return _internal_leaderid();
}
inline void AppendEntriesArgs::_internal_set_leaderid(int32_t value) {
  
  _impl_.leaderid_ = value;
}
inline void AppendEntriesArgs::set_leaderid(int32_t value) {
  _internal_set_leaderid(value);
  // @@protoc_insertion_point(field_set:raftRpcProctoc.AppendEntriesArgs.LeaderId)
}

// int32 PrevLogIndex = 3;
inline void AppendEntriesArgs::clear_prevlogindex() {
  _impl_.prevlogindex_ = 0;
}
inline int32_t AppendEntriesArgs::_internal_prevlogindex() const {
  return _impl_.prevlogindex_;
}
inline int32_t AppendEntriesArgs::prevlogindex() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.AppendEntriesArgs.PrevLogIndex)
  return _internal_prevlogindex();
}
inline void AppendEntriesArgs::_internal_set_prevlogindex(int32_t value) {
  
  _impl_.prevlogindex_ = value;
}
inline void AppendEntriesArgs::set_prevlogindex(int32_t value) {
  _internal_set_prevlogindex(value);
  // @@protoc_insertion_point(field_set:raftRpcProctoc.AppendEntriesArgs.PrevLogIndex)
}

// int32 PrevLogTerm = 4;
inline void AppendEntriesArgs::clear_prevlogterm() {
  _impl_.prevlogterm_ = 0;
}
inline int32_t AppendEntriesArgs::_internal_prevlogterm() const {
  return _impl_.prevlogterm_;
}
inline int32_t AppendEntriesArgs::prevlogterm() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.AppendEntriesArgs.PrevLogTerm)
  return _internal_prevlogterm();
}
inline void AppendEntriesArgs::_internal_set_prevlogterm(int32_t value) {
  
  _impl_.prevlogterm_ = value;
}
inline void AppendEntriesArgs::set_prevlogterm(int32_t value) {
  _internal_set_prevlogterm(value);
  // @@protoc_insertion_point(field_set:raftRpcProctoc.AppendEntriesArgs.PrevLogTerm)
}

// repeated .raftRpcProctoc.LogEntry Entries = 5;
inline int AppendEntriesArgs::_internal_entries_size() const {
  return _impl_.entries_.size();
}
inline int AppendEntriesArgs::entries_size() const {
  return _internal_entries_size();
}
inline void AppendEntriesArgs::clear_entries() {
  _impl_.entries_.Clear();
}
inline ::raftRpcProctoc::LogEntry* AppendEntriesArgs::mutable_entries(int index) {
  // @@protoc_insertion_point(field_mutable:raftRpcProctoc.AppendEntriesArgs.Entries)
  return _impl_.entries_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::raftRpcProctoc::LogEntry >*
AppendEntriesArgs::mutable_entries() {
  // @@protoc_insertion_point(field_mutable_list:raftRpcProctoc.AppendEntriesArgs.Entries)
  return &_impl_.entries_;
}
inline const ::raftRpcProctoc::LogEntry& AppendEntriesArgs::_internal_entries(int index) const {
  return _impl_.entries_.Get(index);
}
inline const ::raftRpcProctoc::LogEntry& AppendEntriesArgs::entries(int index) const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.AppendEntriesArgs.Entries)
  return _internal_entries(index);
}
inline ::raftRpcProctoc::LogEntry* AppendEntriesArgs::_internal_add_entries() {
  return _impl_.entries_.Add();
}
inline ::raftRpcProctoc::LogEntry* AppendEntriesArgs::add_entries() {
  ::raftRpcProctoc::LogEntry* _add = _internal_add_entries();
  // @@protoc_insertion_point(field_add:raftRpcProctoc.AppendEntriesArgs.Entries)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::raftRpcProctoc::LogEntry >&
AppendEntriesArgs::entries() const {
  // @@protoc_insertion_point(field_list:raftRpcProctoc.AppendEntriesArgs.Entries)
  return _impl_.entries_;
}

// int32 LeaderCommit = 6;
inline void AppendEntriesArgs::clear_leadercommit() {
  _impl_.leadercommit_ = 0;
}
inline int32_t AppendEntriesArgs::_internal_leadercommit() const {
  return _impl_.leadercommit_;
}
inline int32_t AppendEntriesArgs::leadercommit() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.AppendEntriesArgs.LeaderCommit)
  return _internal_leadercommit();
}
inline void AppendEntriesArgs::_internal_set_leadercommit(int32_t value) {
  
  _impl_.leadercommit_ = value;
}
inline void AppendEntriesArgs::set_leadercommit(int32_t value) {
  _internal_set_leadercommit(value);
  // @@protoc_insertion_point(field_set:raftRpcProctoc.AppendEntriesArgs.LeaderCommit)
}

// -------------------------------------------------------------------

// AppendEntriesReply

// int32 Term = 1;
inline void AppendEntriesReply::clear_term() {
  _impl_.term_ = 0;
}
inline int32_t AppendEntriesReply::_internal_term() const {
  return _impl_.term_;
}
inline int32_t AppendEntriesReply::term() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.AppendEntriesReply.Term)
  return _internal_term();
}
inline void AppendEntriesReply::_internal_set_term(int32_t value) {
  
  _impl_.term_ = value;
}
inline void AppendEntriesReply::set_term(int32_t value) {
  _internal_set_term(value);
  // @@protoc_insertion_point(field_set:raftRpcProctoc.AppendEntriesReply.Term)
}

// bool Success = 2;
inline void AppendEntriesReply::clear_success() {
  _impl_.success_ = false;
}
inline bool AppendEntriesReply::_internal_success() const {
  return _impl_.success_;
}
inline bool AppendEntriesReply::success() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.AppendEntriesReply.Success)
  return _internal_success();
}
inline void AppendEntriesReply::_internal_set_success(bool value) {
  
  _impl_.success_ = value;
}
inline void AppendEntriesReply::set_success(bool value) {
  _internal_set_success(value);
  // @@protoc_insertion_point(field_set:raftRpcProctoc.AppendEntriesReply.Success)
}

// int32 UpdateNextIndex = 3;
inline void AppendEntriesReply::clear_updatenextindex() {
  _impl_.updatenextindex_ = 0;
}
inline int32_t AppendEntriesReply::_internal_updatenextindex() const {
  return _impl_.updatenextindex_;
}
inline int32_t AppendEntriesReply::updatenextindex() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.AppendEntriesReply.UpdateNextIndex)
  return _internal_updatenextindex();
}
inline void AppendEntriesReply::_internal_set_updatenextindex(int32_t value) {
  
  _impl_.updatenextindex_ = value;
}
inline void AppendEntriesReply::set_updatenextindex(int32_t value) {
  _internal_set_updatenextindex(value);
  // @@protoc_insertion_point(field_set:raftRpcProctoc.AppendEntriesReply.UpdateNextIndex)
}

// int32 AppState = 4;
inline void AppendEntriesReply::clear_appstate() {
  _impl_.appstate_ = 0;
}
inline int32_t AppendEntriesReply::_internal_appstate() const {
  return _impl_.appstate_;
}
inline int32_t AppendEntriesReply::appstate() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.AppendEntriesReply.AppState)
  return _internal_appstate();
}
inline void AppendEntriesReply::_internal_set_appstate(int32_t value) {
  
  _impl_.appstate_ = value;
}
inline void AppendEntriesReply::set_appstate(int32_t value) {
  _internal_set_appstate(value);
  // @@protoc_insertion_point(field_set:raftRpcProctoc.AppendEntriesReply.AppState)
}

// -------------------------------------------------------------------

// RequestVoteArgs

// int32 Term = 1;
inline void RequestVoteArgs::clear_term() {
  _impl_.term_ = 0;
}
inline int32_t RequestVoteArgs::_internal_term() const {
  return _impl_.term_;
}
inline int32_t RequestVoteArgs::term() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.RequestVoteArgs.Term)
  return _internal_term();
}
inline void RequestVoteArgs::_internal_set_term(int32_t value) {
  
  _impl_.term_ = value;
}
inline void RequestVoteArgs::set_term(int32_t value) {
  _internal_set_term(value);
  // @@protoc_insertion_point(field_set:raftRpcProctoc.RequestVoteArgs.Term)
}

// int32 CandidateId = 2;
inline void RequestVoteArgs::clear_candidateid() {
  _impl_.candidateid_ = 0;
}
inline int32_t RequestVoteArgs::_internal_candidateid() const {
  return _impl_.candidateid_;
}
inline int32_t RequestVoteArgs::candidateid() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.RequestVoteArgs.CandidateId)
  return _internal_candidateid();
}
inline void RequestVoteArgs::_internal_set_candidateid(int32_t value) {
  
  _impl_.candidateid_ = value;
}
inline void RequestVoteArgs::set_candidateid(int32_t value) {
  _internal_set_candidateid(value);
  // @@protoc_insertion_point(field_set:raftRpcProctoc.RequestVoteArgs.CandidateId)
}

// int32 LastLogIndex = 3;
inline void RequestVoteArgs::clear_lastlogindex() {
  _impl_.lastlogindex_ = 0;
}
inline int32_t RequestVoteArgs::_internal_lastlogindex() const {
  return _impl_.lastlogindex_;
}
inline int32_t RequestVoteArgs::lastlogindex() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.RequestVoteArgs.LastLogIndex)
  return _internal_lastlogindex();
}
inline void RequestVoteArgs::_internal_set_lastlogindex(int32_t value) {
  
  _impl_.lastlogindex_ = value;
}
inline void RequestVoteArgs::set_lastlogindex(int32_t value) {
  _internal_set_lastlogindex(value);
  // @@protoc_insertion_point(field_set:raftRpcProctoc.RequestVoteArgs.LastLogIndex)
}

// int32 LastLogTerm = 4;
inline void RequestVoteArgs::clear_lastlogterm() {
  _impl_.lastlogterm_ = 0;
}
inline int32_t RequestVoteArgs::_internal_lastlogterm() const {
  return _impl_.lastlogterm_;
}
inline int32_t RequestVoteArgs::lastlogterm() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.RequestVoteArgs.LastLogTerm)
  return _internal_lastlogterm();
}
inline void RequestVoteArgs::_internal_set_lastlogterm(int32_t value) {
  
  _impl_.lastlogterm_ = value;
}
inline void RequestVoteArgs::set_lastlogterm(int32_t value) {
  _internal_set_lastlogterm(value);
  // @@protoc_insertion_point(field_set:raftRpcProctoc.RequestVoteArgs.LastLogTerm)
}

// -------------------------------------------------------------------

// RequestVoteReply

// int32 Term = 1;
inline void RequestVoteReply::clear_term() {
  _impl_.term_ = 0;
}
inline int32_t RequestVoteReply::_internal_term() const {
  return _impl_.term_;
}
inline int32_t RequestVoteReply::term() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.RequestVoteReply.Term)
  return _internal_term();
}
inline void RequestVoteReply::_internal_set_term(int32_t value) {
  
  _impl_.term_ = value;
}
inline void RequestVoteReply::set_term(int32_t value) {
  _internal_set_term(value);
  // @@protoc_insertion_point(field_set:raftRpcProctoc.RequestVoteReply.Term)
}

// bool VoteGranted = 2;
inline void RequestVoteReply::clear_votegranted() {
  _impl_.votegranted_ = false;
}
inline bool RequestVoteReply::_internal_votegranted() const {
  return _impl_.votegranted_;
}
inline bool RequestVoteReply::votegranted() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.RequestVoteReply.VoteGranted)
  return _internal_votegranted();
}
inline void RequestVoteReply::_internal_set_votegranted(bool value) {
  
  _impl_.votegranted_ = value;
}
inline void RequestVoteReply::set_votegranted(bool value) {
  _internal_set_votegranted(value);
  // @@protoc_insertion_point(field_set:raftRpcProctoc.RequestVoteReply.VoteGranted)
}

// int32 VoteState = 3;
inline void RequestVoteReply::clear_votestate() {
  _impl_.votestate_ = 0;
}
inline int32_t RequestVoteReply::_internal_votestate() const {
  return _impl_.votestate_;
}
inline int32_t RequestVoteReply::votestate() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.RequestVoteReply.VoteState)
  return _internal_votestate();
}
inline void RequestVoteReply::_internal_set_votestate(int32_t value) {
  
  _impl_.votestate_ = value;
}
inline void RequestVoteReply::set_votestate(int32_t value) {
  _internal_set_votestate(value);
  // @@protoc_insertion_point(field_set:raftRpcProctoc.RequestVoteReply.VoteState)
}

// -------------------------------------------------------------------

// InstallSnapshotRequest

// int32 LeaderId = 1;
inline void InstallSnapshotRequest::clear_leaderid() {
  _impl_.leaderid_ = 0;
}
inline int32_t InstallSnapshotRequest::_internal_leaderid() const {
  return _impl_.leaderid_;
}
inline int32_t InstallSnapshotRequest::leaderid() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.InstallSnapshotRequest.LeaderId)
  return _internal_leaderid();
}
inline void InstallSnapshotRequest::_internal_set_leaderid(int32_t value) {
  
  _impl_.leaderid_ = value;
}
inline void InstallSnapshotRequest::set_leaderid(int32_t value) {
  _internal_set_leaderid(value);
  // @@protoc_insertion_point(field_set:raftRpcProctoc.InstallSnapshotRequest.LeaderId)
}

// int32 Term = 2;
inline void InstallSnapshotRequest::clear_term() {
  _impl_.term_ = 0;
}
inline int32_t InstallSnapshotRequest::_internal_term() const {
  return _impl_.term_;
}
inline int32_t InstallSnapshotRequest::term() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.InstallSnapshotRequest.Term)
  return _internal_term();
}
inline void InstallSnapshotRequest::_internal_set_term(int32_t value) {
  
  _impl_.term_ = value;
}
inline void InstallSnapshotRequest::set_term(int32_t value) {
  _internal_set_term(value);
  // @@protoc_insertion_point(field_set:raftRpcProctoc.InstallSnapshotRequest.Term)
}

// int32 LastSnapShotIncludeIndex = 3;
inline void InstallSnapshotRequest::clear_lastsnapshotincludeindex() {
  _impl_.lastsnapshotincludeindex_ = 0;
}
inline int32_t InstallSnapshotRequest::_internal_lastsnapshotincludeindex() const {
  return _impl_.lastsnapshotincludeindex_;
}
inline int32_t InstallSnapshotRequest::lastsnapshotincludeindex() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.InstallSnapshotRequest.LastSnapShotIncludeIndex)
  return _internal_lastsnapshotincludeindex();
}
inline void InstallSnapshotRequest::_internal_set_lastsnapshotincludeindex(int32_t value) {
  
  _impl_.lastsnapshotincludeindex_ = value;
}
inline void InstallSnapshotRequest::set_lastsnapshotincludeindex(int32_t value) {
  _internal_set_lastsnapshotincludeindex(value);
  // @@protoc_insertion_point(field_set:raftRpcProctoc.InstallSnapshotRequest.LastSnapShotIncludeIndex)
}

// int32 LastSnapShotIncludeTerm = 4;
inline void InstallSnapshotRequest::clear_lastsnapshotincludeterm() {
  _impl_.lastsnapshotincludeterm_ = 0;
}
inline int32_t InstallSnapshotRequest::_internal_lastsnapshotincludeterm() const {
  return _impl_.lastsnapshotincludeterm_;
}
inline int32_t InstallSnapshotRequest::lastsnapshotincludeterm() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.InstallSnapshotRequest.LastSnapShotIncludeTerm)
  return _internal_lastsnapshotincludeterm();
}
inline void InstallSnapshotRequest::_internal_set_lastsnapshotincludeterm(int32_t value) {
  
  _impl_.lastsnapshotincludeterm_ = value;
}
inline void InstallSnapshotRequest::set_lastsnapshotincludeterm(int32_t value) {
  _internal_set_lastsnapshotincludeterm(value);
  // @@protoc_insertion_point(field_set:raftRpcProctoc.InstallSnapshotRequest.LastSnapShotIncludeTerm)
}

// bytes Data = 5;
inline void InstallSnapshotRequest::clear_data() {
  _impl_.data_.ClearToEmpty();
}
inline const std::string& InstallSnapshotRequest::data() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.InstallSnapshotRequest.Data)
  return _internal_data();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void InstallSnapshotRequest::set_data(ArgT0&& arg0, ArgT... args) {
 
 _impl_.data_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:raftRpcProctoc.InstallSnapshotRequest.Data)
}
inline std::string* InstallSnapshotRequest::mutable_data() {
  std::string* _s = _internal_mutable_data();
  // @@protoc_insertion_point(field_mutable:raftRpcProctoc.InstallSnapshotRequest.Data)
  return _s;
}
inline const std::string& InstallSnapshotRequest::_internal_data() const {
  return _impl_.data_.Get();
}
inline void InstallSnapshotRequest::_internal_set_data(const std::string& value) {
  
  _impl_.data_.Set(value, GetArenaForAllocation());
}
inline std::string* InstallSnapshotRequest::_internal_mutable_data() {
  
  return _impl_.data_.Mutable(GetArenaForAllocation());
}
inline std::string* InstallSnapshotRequest::release_data() {
  // @@protoc_insertion_point(field_release:raftRpcProctoc.InstallSnapshotRequest.Data)
  return _impl_.data_.Release();
}
inline void InstallSnapshotRequest::set_allocated_data(std::string* data) {
  if (data != nullptr) {
    
  } else {
    
  }
  _impl_.data_.SetAllocated(data, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.data_.IsDefault()) {
    _impl_.data_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:raftRpcProctoc.InstallSnapshotRequest.Data)
}

// -------------------------------------------------------------------

// InstallSnapshotResponse

// int32 Term = 1;
inline void InstallSnapshotResponse::clear_term() {
  _impl_.term_ = 0;
}
inline int32_t InstallSnapshotResponse::_internal_term() const {
  return _impl_.term_;
}
inline int32_t InstallSnapshotResponse::term() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.InstallSnapshotResponse.Term)
  return _internal_term();
}
inline void InstallSnapshotResponse::_internal_set_term(int32_t value) {
  
  _impl_.term_ = value;
}
inline void InstallSnapshotResponse::set_term(int32_t value) {
  _internal_set_term(value);
  // @@protoc_insertion_point(field_set:raftRpcProctoc.InstallSnapshotResponse.Term)
}

// -------------------------------------------------------------------

// ChangeConfigArgs

// .raftRpcProctoc.ConfigChangeType Type = 1;
inline void ChangeConfigArgs::clear_type() {
  _impl_.type_ = 0;
}
inline ::raftRpcProctoc::ConfigChangeType ChangeConfigArgs::_internal_type() const {
  return static_cast< ::raftRpcProctoc::ConfigChangeType >(_impl_.type_);
}
inline ::raftRpcProctoc::ConfigChangeType ChangeConfigArgs::type() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.ChangeConfigArgs.Type)
  return _internal_type();
}
inline void ChangeConfigArgs::_internal_set_type(::raftRpcProctoc::ConfigChangeType value) {
  
  _impl_.type_ = value;
}
inline void ChangeConfigArgs::set_type(::raftRpcProctoc::ConfigChangeType value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:raftRpcProctoc.ChangeConfigArgs.Type)
}

// string NodeId = 2;
inline void ChangeConfigArgs::clear_nodeid() {
  _impl_.nodeid_.ClearToEmpty();
}
inline const std::string& ChangeConfigArgs::nodeid() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.ChangeConfigArgs.NodeId)
  return _internal_nodeid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ChangeConfigArgs::set_nodeid(ArgT0&& arg0, ArgT... args) {
 
 _impl_.nodeid_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:raftRpcProctoc.ChangeConfigArgs.NodeId)
}
inline std::string* ChangeConfigArgs::mutable_nodeid() {
  std::string* _s = _internal_mutable_nodeid();
  // @@protoc_insertion_point(field_mutable:raftRpcProctoc.ChangeConfigArgs.NodeId)
  return _s;
}
inline const std::string& ChangeConfigArgs::_internal_nodeid() const {
  return _impl_.nodeid_.Get();
}
inline void ChangeConfigArgs::_internal_set_nodeid(const std::string& value) {
  
  _impl_.nodeid_.Set(value, GetArenaForAllocation());
}
inline std::string* ChangeConfigArgs::_internal_mutable_nodeid() {
  
  return _impl_.nodeid_.Mutable(GetArenaForAllocation());
}
inline std::string* ChangeConfigArgs::release_nodeid() {
  // @@protoc_insertion_point(field_release:raftRpcProctoc.ChangeConfigArgs.NodeId)
  return _impl_.nodeid_.Release();
}
inline void ChangeConfigArgs::set_allocated_nodeid(std::string* nodeid) {
  if (nodeid != nullptr) {
    
  } else {
    
  }
  _impl_.nodeid_.SetAllocated(nodeid, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.nodeid_.IsDefault()) {
    _impl_.nodeid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:raftRpcProctoc.ChangeConfigArgs.NodeId)
}

// string Address = 3;
inline void ChangeConfigArgs::clear_address() {
  _impl_.address_.ClearToEmpty();
}
inline const std::string& ChangeConfigArgs::address() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.ChangeConfigArgs.Address)
  return _internal_address();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ChangeConfigArgs::set_address(ArgT0&& arg0, ArgT... args) {
 
 _impl_.address_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:raftRpcProctoc.ChangeConfigArgs.Address)
}
inline std::string* ChangeConfigArgs::mutable_address() {
  std::string* _s = _internal_mutable_address();
  // @@protoc_insertion_point(field_mutable:raftRpcProctoc.ChangeConfigArgs.Address)
  return _s;
}
inline const std::string& ChangeConfigArgs::_internal_address() const {
  return _impl_.address_.Get();
}
inline void ChangeConfigArgs::_internal_set_address(const std::string& value) {
  
  _impl_.address_.Set(value, GetArenaForAllocation());
}
inline std::string* ChangeConfigArgs::_internal_mutable_address() {
  
  return _impl_.address_.Mutable(GetArenaForAllocation());
}
inline std::string* ChangeConfigArgs::release_address() {
  // @@protoc_insertion_point(field_release:raftRpcProctoc.ChangeConfigArgs.Address)
  return _impl_.address_.Release();
}
inline void ChangeConfigArgs::set_allocated_address(std::string* address) {
  if (address != nullptr) {
    
  } else {
    
  }
  _impl_.address_.SetAllocated(address, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.address_.IsDefault()) {
    _impl_.address_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:raftRpcProctoc.ChangeConfigArgs.Address)
}

// -------------------------------------------------------------------

// ChangeConfigReply

// bool Success = 1;
inline void ChangeConfigReply::clear_success() {
  _impl_.success_ = false;
}
inline bool ChangeConfigReply::_internal_success() const {
  return _impl_.success_;
}
inline bool ChangeConfigReply::success() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.ChangeConfigReply.Success)
  return _internal_success();
}
inline void ChangeConfigReply::_internal_set_success(bool value) {
  
  _impl_.success_ = value;
}
inline void ChangeConfigReply::set_success(bool value) {
  _internal_set_success(value);
  // @@protoc_insertion_point(field_set:raftRpcProctoc.ChangeConfigReply.Success)
}

// string Error = 2;
inline void ChangeConfigReply::clear_error() {
  _impl_.error_.ClearToEmpty();
}
inline const std::string& ChangeConfigReply::error() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.ChangeConfigReply.Error)
  return _internal_error();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ChangeConfigReply::set_error(ArgT0&& arg0, ArgT... args) {
 
 _impl_.error_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:raftRpcProctoc.ChangeConfigReply.Error)
}
inline std::string* ChangeConfigReply::mutable_error() {
  std::string* _s = _internal_mutable_error();
  // @@protoc_insertion_point(field_mutable:raftRpcProctoc.ChangeConfigReply.Error)
  return _s;
}
inline const std::string& ChangeConfigReply::_internal_error() const {
  return _impl_.error_.Get();
}
inline void ChangeConfigReply::_internal_set_error(const std::string& value) {
  
  _impl_.error_.Set(value, GetArenaForAllocation());
}
inline std::string* ChangeConfigReply::_internal_mutable_error() {
  
  return _impl_.error_.Mutable(GetArenaForAllocation());
}
inline std::string* ChangeConfigReply::release_error() {
  // @@protoc_insertion_point(field_release:raftRpcProctoc.ChangeConfigReply.Error)
  return _impl_.error_.Release();
}
inline void ChangeConfigReply::set_allocated_error(std::string* error) {
  if (error != nullptr) {
    
  } else {
    
  }
  _impl_.error_.SetAllocated(error, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.error_.IsDefault()) {
    _impl_.error_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:raftRpcProctoc.ChangeConfigReply.Error)
}

// bool IsLeader = 3;
inline void ChangeConfigReply::clear_isleader() {
  _impl_.isleader_ = false;
}
inline bool ChangeConfigReply::_internal_isleader() const {
  return _impl_.isleader_;
}
inline bool ChangeConfigReply::isleader() const {
  // @@protoc_insertion_point(field_get:raftRpcProctoc.ChangeConfigReply.IsLeader)
  return _internal_isleader();
}
inline void ChangeConfigReply::_internal_set_isleader(bool value) {
  
  _impl_.isleader_ = value;
}
inline void ChangeConfigReply::set_isleader(bool value) {
  _internal_set_isleader(value);
  // @@protoc_insertion_point(field_set:raftRpcProctoc.ChangeConfigReply.IsLeader)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace raftRpcProctoc

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::raftRpcProctoc::ConfigChangeType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::raftRpcProctoc::ConfigChangeType>() {
  return ::raftRpcProctoc::ConfigChangeType_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_raftRpcPro_2fraftRPC_2eproto
