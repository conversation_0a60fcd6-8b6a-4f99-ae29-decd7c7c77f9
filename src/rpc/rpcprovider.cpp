#include "rpcprovider.h"
#include <arpa/inet.h>
#include <netdb.h>
#include <unistd.h>
#include <cstring>
#include <fstream>
#include <string>
#include "rpcheader.pb.h"
#include "util.h"

/**
 * @brief 注册RPC服务
 * @param service 要注册的服务对象指针
 *
 * 这里是框架提供给外部使用的，可以发布rpc方法的函数接口
 * 只是简单的把服务描述符和方法描述符全部保存在本地而已
 *
 * 服务映射结构：
 * service_name =>  service描述
 *                        =》 service* 记录服务对象
 *                        method_name  =>  method方法对象
 */
void RpcProvider::NotifyService(google::protobuf::Service *service)
{
  ServiceInfo service_info;

  // 获取了服务对象的描述信息
  const google::protobuf::ServiceDescriptor *pserviceDesc = service->GetDescriptor();
  // 获取服务的名字
  std::string service_name = pserviceDesc->name();
  // 获取服务对象service的方法的数量
  int methodCnt = pserviceDesc->method_count();

  std::cout << "service_name:" << service_name << std::endl;

  // 遍历服务的所有方法，建立方法映射
  for (int i = 0; i < methodCnt; ++i)
  {
    // 获取了服务对象指定下标的服务方法的描述（抽象描述） UserService   Login
    const google::protobuf::MethodDescriptor *pmethodDesc = pserviceDesc->method(i);
    std::string method_name = pmethodDesc->name();
    service_info.m_methodMap.insert({method_name, pmethodDesc}); // 将方法名和方法描述符插入映射表
  }
  service_info.m_service = service;                  // 保存服务对象
  m_serviceMap.insert({service_name, service_info}); // 将服务信息插入服务映射表
}

/**
 * @brief 启动RPC服务节点
 * @param nodeIndex 节点索引
 * @param port 服务端口
 *
 * 启动RPC服务节点，开始提供RPC远程网络调用服务
 */
void RpcProvider::Run(int nodeIndex, short port)
{
  // 获取可用IP地址
  char *ipC;
  char hname[128];
  struct hostent *hent;
  gethostname(hname, sizeof(hname)); // 获取主机名
  hent = gethostbyname(hname);       // 根据主机名获取主机信息
  for (int i = 0; hent->h_addr_list[i]; i++)
  {
    ipC = inet_ntoa(*(struct in_addr *)(hent->h_addr_list[i])); // 将网络字节序转换为点分十进制IP地址
  }
  std::string ip = std::string(ipC);

  // 写入配置文件 "test.conf"
  std::string node = "node" + std::to_string(nodeIndex);
  std::ofstream outfile;
  outfile.open("test.conf", std::ios::app); // 打开文件并追加写入
  if (!outfile.is_open())
  {
    std::cout << "打开文件失败！" << std::endl;
    exit(EXIT_FAILURE);
  }
  outfile << node + "ip=" + ip << std::endl;                     // 写入节点IP
  outfile << node + "port=" + std::to_string(port) << std::endl; // 写入节点端口
  outfile.close();

  // 创建服务器地址
  muduo::net::InetAddress address(ip, port);

  // 创建TcpServer对象
  m_muduo_server = std::make_shared<muduo::net::TcpServer>(&m_eventLoop, address, "RpcProvider");

  // 绑定连接回调和消息读写回调方法，分离了网络代码和业务代码
  /*
  bind的作用：
  如果不使用std::bind将回调函数和TcpConnection对象绑定起来，那么在回调函数中就无法直接访问和修改TcpConnection对象的状态。因为回调函数是作为一个独立的函数被调用的，它没有当前对象的上下文信息（即this指针），也就无法直接访问当前对象的状态。
  如果要在回调函数中访问和修改TcpConnection对象的状态，需要通过参数的形式将当前对象的指针传递进去，并且保证回调函数在当前对象的上下文环境中被调用。这种方式比较复杂，容易出错，也不便于代码的编写和维护。因此，使用std::bind将回调函数和TcpConnection对象绑定起来，可以更加方便、直观地访问和修改对象的状态，同时也可以避免一些常见的错误。
  */
  m_muduo_server->setConnectionCallback(std::bind(&RpcProvider::OnConnection, this, std::placeholders::_1));
  m_muduo_server->setMessageCallback(
      std::bind(&RpcProvider::OnMessage, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));

  // 设置muduo库的线程数量
  m_muduo_server->setThreadNum(4);

  // RPC服务端准备启动，打印信息
  std::cout << "RpcProvider start service at ip:" << ip << " port:" << port << std::endl;

  // 启动网络服务
  m_muduo_server->start();
  m_eventLoop.loop();
  /*
  这段代码是在启动网络服务和事件循环，其中server是一个TcpServer对象，m_eventLoop是一个EventLoop对象。

首先调用server.start()函数启动网络服务。在Muduo库中，TcpServer类封装了底层网络操作，包括TCP连接的建立和关闭、接收客户端数据、发送数据给客户端等等。通过调用TcpServer对象的start函数，可以启动底层网络服务并监听客户端连接的到来。

接下来调用m_eventLoop.loop()函数启动事件循环。在Muduo库中，EventLoop类封装了事件循环的核心逻辑，包括定时器、IO事件、信号等等。通过调用EventLoop对象的loop函数，可以启动事件循环，等待事件的到来并处理事件。

在这段代码中，首先启动网络服务，然后进入事件循环阶段，等待并处理各种事件。网络服务和事件循环是两个相对独立的模块，它们的启动顺序和调用方式都是确定的。启动网络服务通常是在事件循环之前，因为网络服务是事件循环的基础。启动事件循环则是整个应用程序的核心，所有的事件都在事件循环中被处理。
  */
}

/**
 * @brief 新的socket连接回调
 * @param conn TCP连接指针
 *
 * 处理新的TCP连接，如果是连接断开则关闭连接
 */
void RpcProvider::OnConnection(const muduo::net::TcpConnectionPtr &conn)
{
  // 如果是新连接就什么都不干，即正常的接收连接即可
  if (!conn->connected())
  {
    // 和RPC客户端的连接断开了
    conn->shutdown(); // 关闭连接
  }
}

/**
 * @brief 已建立连接用户的读写事件回调
 * @param conn TCP连接指针
 * @param buffer 数据缓冲区
 * @param receiveTime 接收时间戳
 *
 * 如果远程有一个RPC服务的调用请求，那么OnMessage方法就会响应
 * 这里来的肯定是一个远程调用请求
 * 因此本函数需要：解析请求，根据服务名，方法名，参数，来调用service的来callmethod来调用本地的业务
 *
 * 数据格式说明：
 * 在框架内部，RpcProvider和RpcConsumer协商好之间通信用的protobuf数据类型
 * service_name method_name args    定义proto的message类型，进行数据头的序列化和反序列化
 *                                 service_name method_name args_size
 * 16UserServiceLoginzhang san123456
 *
 * header_size(4个字节) + header_str + args_str
 * 10 "10"
 * 10000 "1000000"
 * std::string   insert和copy方法
 */
void RpcProvider::OnMessage(const muduo::net::TcpConnectionPtr &conn, muduo::net::Buffer *buffer, muduo::Timestamp)
{
  // 网络上接收的远程RPC调用请求的字符流    Login args
  std::string recv_buf = buffer->retrieveAllAsString();

  // 使用protobuf的CodedInputStream来解析数据流
  google::protobuf::io::ArrayInputStream array_input(recv_buf.data(), recv_buf.size());
  google::protobuf::io::CodedInputStream coded_input(&array_input);
  uint32_t header_size{};

  coded_input.ReadVarint32(&header_size); // 解析header_size

  // 根据header_size读取数据头的原始字符流，反序列化数据，得到RPC请求的详细信息
  std::string rpc_header_str;
  mprpc::RpcHeader rpcHeader;
  std::string service_name;
  std::string method_name;

  // 设置读取限制，不必担心数据读多
  google::protobuf::io::CodedInputStream::Limit msg_limit = coded_input.PushLimit(header_size);
  coded_input.ReadString(&rpc_header_str, header_size); // 读取头部数据
  // 恢复之前的限制，以便安全地继续读取其他数据
  coded_input.PopLimit(msg_limit);
  uint32_t args_size{};
  if (rpcHeader.ParseFromString(rpc_header_str))
  {
    // 数据头反序列化成功
    service_name = rpcHeader.service_name();
    method_name = rpcHeader.method_name();
    args_size = rpcHeader.args_size();
  }
  else
  {
    // 数据头反序列化失败
    std::cout << "rpc_header_str:" << rpc_header_str << " parse error!" << std::endl;
    return;
  }

  // 获取RPC方法参数的字符流数据
  std::string args_str;
  // 直接读取args_size长度的字符串数据
  bool read_args_success = coded_input.ReadString(&args_str, args_size);

  if (!read_args_success)
  {
    // 处理错误：参数数据读取失败
    return;
  }

  // 获取service对象和method对象
  auto it = m_serviceMap.find(service_name); // 查找服务
  if (it == m_serviceMap.end())
  {
    std::cout << "服务：" << service_name << " is not exist!" << std::endl;
    std::cout << "当前已经有的服务列表为:";
    for (auto item : m_serviceMap)
    {
      std::cout << item.first << " ";
    }
    std::cout << std::endl;
    return;
  }

  auto mit = it->second.m_methodMap.find(method_name); // 查找方法
  if (mit == it->second.m_methodMap.end())
  {
    std::cout << service_name << ":" << method_name << " is not exist!" << std::endl;
    return;
  }

  google::protobuf::Service *service = it->second.m_service;      // 获取service对象  new UserService
  const google::protobuf::MethodDescriptor *method = mit->second; // 获取method对象  Login

  // 生成RPC方法调用的请求request和响应response参数,由于是RPC的请求，因此请求需要通过request来序列化
  google::protobuf::Message *request = service->GetRequestPrototype(method).New(); // 创建请求对象
  if (!request->ParseFromString(args_str))                                         // 反序列化请求参数
  {
    std::cout << "request parse error, content:" << args_str << std::endl;
    return;
  }
  google::protobuf::Message *response = service->GetResponsePrototype(method).New(); // 创建响应对象

  // 给method方法的调用，绑定一个Closure的回调函数
  // closure是执行完本地方法之后会发生的回调，因此需要完成序列化和反向发送请求的操作
  google::protobuf::Closure *done =
      google::protobuf::NewCallback<RpcProvider, const muduo::net::TcpConnectionPtr &, google::protobuf::Message *>(
          this, &RpcProvider::SendRpcResponse, conn, response);

  // 真正调用方法
  service->CallMethod(method, nullptr, request, response, done);
}

/**
 * @brief Closure的回调操作，用于序列化RPC的响应和网络发送
 * @param conn TCP连接指针
 * @param response RPC响应消息
 *
 * 发送响应回去，将响应序列化后通过网络发送给RPC调用方
 */
void RpcProvider::SendRpcResponse(const muduo::net::TcpConnectionPtr &conn, google::protobuf::Message *response)
{
  std::string response_str;
  if (response->SerializeToString(&response_str)) // response进行序列化
  {
    // 序列化成功后，通过网络把RPC方法执行的结果发送会RPC的调用方
    conn->send(response_str);
  }
  else
  {
    std::cout << "serialize response_str error!" << std::endl;
  }
}

/**
 * @brief 启动RPC服务节点（指定IP地址）
 * @param ip 服务IP地址
 * @param port 服务端口
 *
 * 启动RPC服务节点，使用指定的IP地址，避免自动检测IP导致的地址不匹配问题
 */
void RpcProvider::Run(const std::string &ip, short port)
{
  // 创建服务器地址
  muduo::net::InetAddress address(ip, port);

  // 创建TcpServer对象
  m_muduo_server = std::make_shared<muduo::net::TcpServer>(&m_eventLoop, address, "RpcProvider");

  // 绑定连接回调和消息读写回调方法
  m_muduo_server->setConnectionCallback(std::bind(&RpcProvider::OnConnection, this, std::placeholders::_1));
  m_muduo_server->setMessageCallback(
      std::bind(&RpcProvider::OnMessage, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));

  // 设置muduo库的线程数量
  m_muduo_server->setThreadNum(4);

  // RPC服务端准备启动，打印信息
  std::cout << "RpcProvider start service at ip:" << ip << " port:" << port << std::endl;

  // 启动网络服务
  m_muduo_server->start();
  m_eventLoop.loop();
}

/**
 * @brief 启动RPC服务节点（带就绪通知）
 * @param ip 服务IP地址
 * @param port 服务端口
 * @param readyCallback 服务就绪后的回调函数
 *
 * 启动RPC服务节点，当服务完全就绪后调用回调函数通知外部
 */
void RpcProvider::Run(const std::string &ip, short port, std::function<void()> readyCallback)
{
  // 创建服务器地址
  muduo::net::InetAddress address(ip, port);

  // 创建TcpServer对象
  m_muduo_server = std::make_shared<muduo::net::TcpServer>(&m_eventLoop, address, "RpcProvider");

  // 绑定连接回调和消息读写回调方法
  m_muduo_server->setConnectionCallback(std::bind(&RpcProvider::OnConnection, this, std::placeholders::_1));
  m_muduo_server->setMessageCallback(
      std::bind(&RpcProvider::OnMessage, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));

  // 设置muduo库的线程数量
  m_muduo_server->setThreadNum(4);

  // RPC服务端准备启动，打印信息
  std::cout << "RpcProvider start service at ip:" << ip << " port:" << port << std::endl;

  // 启动网络服务
  m_muduo_server->start();

  // 在事件循环中安排回调执行，确保服务完全就绪
  m_eventLoop.runAfter(0.1, readyCallback); // 100ms后执行回调，确保服务已启动

  m_eventLoop.loop();
}

/**
 * @brief 析构函数
 *
 * 清理资源，停止事件循环
 */
RpcProvider::~RpcProvider()
{
  std::cout << "[func - RpcProvider::~RpcProvider()]: ip和port信息：" << m_muduo_server->ipPort() << std::endl;
  m_eventLoop.quit(); // 退出事件循环
}
